/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.log;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.datastore.DatastoreConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.eventbus.EventEngine;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.DateTimeUtil;
import com.mindarray.util.Logger;
import com.mindarray.util.MotadataConfigUtil;
import io.vertx.core.AbstractVerticle;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonObject;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

import static com.mindarray.GlobalConstants.BootstrapType;
import static com.mindarray.GlobalConstants.SEPARATOR_WITH_ESCAPE;
import static com.mindarray.eventbus.EventBusConstants.*;
import static com.mindarray.log.LogEngineConstants.*;

/**
 * A verticle responsible for calculating and storing log statistics.
 * <p>
 * The LogStatCalculator tracks statistics for log events, such as:
 * <ul>
 *   <li>Number of logs per second</li>
 *   <li>Log volume in bytes per second</li>
 *   <li>Total log volume</li>
 * </ul>
 * <p>
 * It maintains a map of statistics for different log sources and categories,
 * incrementing counters as log events are received. Periodically, it calculates
 * derived statistics (like logs per second) and flushes these statistics to storage.
 * <p>
 * The statistics are stored with the following dimensions:
 * <ul>
 *   <li>Event source (IP address or hostname)</li>
 *   <li>Event category</li>
 *   <li>Event source type</li>
 * </ul>
 * <p>
 * These statistics can be used for monitoring log volume trends and detecting
 * unusual patterns in log generation.
 */
public class LogStatCalculator extends AbstractVerticle
{
    /**
     * Logger for this class
     */
    private static final Logger LOGGER = new Logger(LogStatCalculator.class, GlobalConstants.MOTADATA_LOG, "Log Stat Calculator");

    /**
     * Interval (in seconds) for flushing log statistics to storage
     */
    private static final int LOG_STAT_FLUSH_TIMER_SECONDS = MotadataConfigUtil.getLogStatFlushTimerSeconds();

    /**
     * Map of statistics indexed by stat key (source + category + source type)
     */
    private final Map<String, int[]> stats = new HashMap<>();

    /**
     * JsonObject for building event data
     */
    private final JsonObject event = new JsonObject();

    /**
     * StringBuilder for building strings efficiently
     */
    private final StringBuilder builder = new StringBuilder(0);

    /**
     * Event engine for handling log events
     */
    private EventEngine eventEngine;

    /**
     * Set of column mappers
     */
    private Set<String> mappers;


    @Override
    public void start(Promise<Void> promise) throws Exception
    {
        try
        {
            if (Bootstrap.bootstrapType() == BootstrapType.APP)
            {
                mappers = new HashSet<>();

                eventEngine = new EventEngine().setLogger(LOGGER)
                        .setEventType(config().getString(EventBusConstants.EVENT_TYPE)).setPersistEventOffset(true)
                        .setEventHandler(event ->
                        {

                            try
                            {
                                stats.computeIfAbsent(event.getString(EVENT_STAT_KEY), value -> new int[2]);

                                var values = stats.get(event.getString(EVENT_STAT_KEY));

                                values[0]++; //count

                                values[1] += event.getInteger(EVENT_VOLUME_BYTES); //volume bytes
                            }
                            catch (Exception exception)
                            {
                                LOGGER.error(exception);
                            }
                        }).start(vertx, promise);

                vertx.setPeriodic(LOG_STAT_FLUSH_TIMER_SECONDS * 1000L, timer -> calculate());

                promise.future().onComplete(result -> LOGGER.debug(config().getString(EventBusConstants.EVENT_TYPE) + " started successfully!!!"));
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            promise.fail(exception);
        }
    }

    private void calculate()
    {
        stats.keySet().forEach(key ->
        {
            var values = stats.get(key);

            var tokens = key.split(SEPARATOR_WITH_ESCAPE);

            if (values[0] > 0) //count
            {
                DatastoreConstants.write(this.event.put(EVENT_CATEGORY, tokens[1]).put(EVENT_SOURCE_TYPE, tokens[2]).put(EVENT_SOURCE, tokens[0])
                        .put(LOG_VOLUME_BYTES, values[1])
                        .put(LOGS_PER_SEC, Math.round(CommonUtil.getDouble(values[0] / LOG_STAT_FLUSH_TIMER_SECONDS)))
                        .put(LOG_VOLUME_BYTES_PER_SEC, Math.round(CommonUtil.getDouble(values[1] / LOG_STAT_FLUSH_TIMER_SECONDS)))
                        .put(GlobalConstants.PLUGIN_ID, DatastoreConstants.PluginId.LOG_EVENT_STAT.getName())
                        .put(EVENT_TIMESTAMP, DateTimeUtil.currentSeconds())
                        .put(DatastoreConstants.DATASTORE_TYPE, DatastoreConstants.DatastoreType.LOG.ordinal()), "log.stat", mappers, builder);
            }

            values[0] = 0; //reset count

            values[1] = 0; // reset volume bytes

            this.event.clear();
        });
    }


    @Override
    public void stop(Promise<Void> promise)
    {
        eventEngine.stop(vertx, promise);
    }
}
