/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 *	Change Logs:
 *	Date			Author			    Notes
 *  28-Feb-2025		Darshan Parmar		MOTADATA-5215: SonarQube Suggestions Resolution
 *  19-Mar-2025     Arpit Shah          MOTADATA-5311: VAPT | Low level user should be able to see role assigned
 *                                      only which avoids excessive data exposure
 */
package com.mindarray.api;

import com.mindarray.ErrorCodes;
import com.mindarray.GlobalConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.store.UserConfigStore;
import com.mindarray.store.UserRoleConfigStore;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import io.vertx.core.Future;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.Router;
import io.vertx.ext.web.RoutingContext;
import jdk.jfr.Description;
import org.apache.http.HttpStatus;

import static com.mindarray.ErrorMessageConstants.INTERNAL_SERVER_EXCEPTION;
import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.APIConstants.*;

public class UserRole extends AbstractAPI
{

    public static final String USER_ROLE_NAME = "user.role.name";
    public static final String USER_ROLE_CONTEXT = "user.role.context";
    public static final String ADMIN_ROLE = "admin.role";
    public static final long USER_ROLE_READ_ONLY = 10000000000002L;
    private static final Logger LOGGER = new Logger(UserRole.class, GlobalConstants.MOTADATA_API, "User Role API");

    public UserRole()
    {
        super("user-roles", UserRoleConfigStore.getStore(), new Logger(UserRole.class, MOTADATA_API, "User Role API"));

    }

    @Override
    public void init(Router router)
    {
        super.init(router);

        router.get("/" + endpoint + "/:id/references").handler(this::getReferences);
    }

    /**
     * Restricts data visibility based on user roles.
     * <p>
     * - Users with lower-level roles can only see their assigned roles to minimize data exposure. <br>
     * - Admin users can access all data.
     * </p>
     *
     * @param routingContext The Vert.x {@link io.vertx.ext.web.RoutingContext} containing request details.
     */
    @Override
    protected void getAll(RoutingContext routingContext)
    {
        try
        {
            if (UserConfigStore.getStore().getItemByValue(User.USER_NAME, routingContext.user().
                    principal().getString(User.USER_NAME)).getLong(User.USER_ROLE).equals(DEFAULT_ID))
            {
                super.getAll(routingContext);
            }
            else
            {
                this.beforeGetAll(routingContext).compose(handler ->

                        Future.<JsonArray>future(promise ->
                                this.configStore.getReferenceCountsByItem().onComplete(result ->
                                {
                                    if (result.succeeded())
                                    {
                                        this.getEntityCountPreHook(result.result()).onComplete(asyncResult ->
                                        {
                                            if (asyncResult.succeeded())
                                            {
                                                var item = this.configStore.getItem(UserConfigStore.getStore().getItemByValue(User.USER_NAME, routingContext.user().
                                                        principal().getString(User.USER_NAME)).getLong(User.USER_ROLE));

                                                var items = new JsonArray();

                                                item = CommonUtil.removeSensitiveFields(item, true);

                                                if (asyncResult.result() != null && asyncResult.result().containsKey(CommonUtil.getString(item.getLong(ID))))
                                                {
                                                    item.put(ENTITY_PROPERTY_COUNT, asyncResult.result().getInteger(CommonUtil.getString(item.getLong(ID))));
                                                }

                                                items.add(item);

                                                promise.complete(items);
                                            }

                                            else
                                            {
                                                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_INTERNAL_SERVER_ERROR).put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL)
                                                        .put(MESSAGE, String.format(INTERNAL_SERVER_EXCEPTION, asyncResult.cause().getMessage()))
                                                        .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                                                        .put(GlobalConstants.ERROR, CommonUtil.formatStackTrace(asyncResult.cause().getStackTrace())));

                                                promise.fail(asyncResult.cause());
                                            }
                                        });
                                    }

                                    else
                                    {
                                        this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_INTERNAL_SERVER_ERROR).put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL)
                                                .put(MESSAGE, String.format(INTERNAL_SERVER_EXCEPTION, result.cause().getMessage()))
                                                .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                                                .put(GlobalConstants.ERROR, CommonUtil.formatStackTrace(result.cause().getStackTrace())));

                                        promise.fail(result.cause());
                                    }
                                })).compose(entities -> this.afterGetAll(entities, routingContext)));
            }
        }
        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }
    }

    @Override
    protected Future<JsonObject> getReferencesPreHook(RoutingContext routingContext, JsonObject response)
    {
        try
        {
            if (!routingContext.user().principal().getJsonArray(APIConstants.USER_PERMISSIONS).contains(USER_SETTINGS + ":" + READ_WRITE_PERMISSION) && (response != null && !response.isEmpty() && response.containsKey(APIConstants.Entity.USER.getName())))
            {
                var users = new JsonArray();

                var entities = response.getJsonArray(APIConstants.Entity.USER.getName());

                for (var index = 0; index < entities.size(); index++)
                {
                    if (entities.getJsonObject(index).getLong(ID).equals(routingContext.user().attributes().getJsonObject("accessToken").getLong(ID)))
                    {
                        users.add(entities.getJsonObject(index));
                    }
                }

                response.put(APIConstants.Entity.USER.getName(), users);
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return Future.succeededFuture(response);
    }

    /*
    This method is used to do post operation of user role update api and publish user role change event to ui
    */
    @Override
    @Description("This method is used to do post operation of user role update api and publish user role change event to ui")
    protected Future<JsonObject> afterUpdate(JsonObject entity, RoutingContext routingContext)
    {
        EventBusConstants.publish(EventBusConstants.EVENT_CONFIG_CHANGE, entity.put(APIConstants.REQUEST, APIConstants.REQUEST_UPDATE).put(APIConstants.ENTITY_NAME, APIConstants.Entity.USER_ROLE.getName()));

        this.send(routingContext, entity);

        return Future.succeededFuture();
    }
}
