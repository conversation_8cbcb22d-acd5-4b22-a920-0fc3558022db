/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.api;

import com.mindarray.Bootstrap;
import com.mindarray.ErrorMessageConstants;
import com.mindarray.GlobalConstants;
import com.mindarray.InfoMessageConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.flow.FlowListener;
import com.mindarray.store.FlowSettingsConfigStore;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.LicenseUtil;
import com.mindarray.util.Logger;
import com.mindarray.util.PortUtil;
import io.vertx.core.Future;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.Router;
import io.vertx.ext.web.RoutingContext;
import org.apache.http.HttpStatus;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.APIConstants.RESPONSE_CODE;
import static com.mindarray.eventbus.EventBusConstants.*;

public class FlowSettings extends AbstractAPI
{
    public static final String FLOW_SETTINGS_SFLOW_DIRECTION = "flow.settings.sflow.direction";   // sFlow V5 Direction only
    public static final String FLOW_SETTINGS_SFLOW_PORT = "flow.settings.sflow.port";
    public static final String FLOW_SETTINGS_NETFLOW_PORT = "flow.settings.netflow.port";
    public static final String FLOW_SETTINGS_BGP_FLOW_ENABLED = "flow.settings.bgp.flow.enabled";
    public static final String FLOW_SETTINGS_BGP_SFLOW_PORT = "flow.settings.bgp.sflow.port";
    public static final String FLOW_SETTINGS_BGP_NETFLOW_PORT = "flow.settings.bgp.netflow.port";
    public static final String FLOW_SETTINGS_AGGREGATION_INTERVAL_MINUTES = "flow.settings.aggregation.interval.minutes";
    private static final Logger LOGGER = new Logger(FlowSettings.class, GlobalConstants.MOTADATA_API, "Flow Settings API");

    public FlowSettings()
    {
        super("flow-settings", FlowSettingsConfigStore.getStore(), LOGGER);
    }

    @Override
    public void init(Router router)
    {
        router.get("/" + endpoint).handler(this::get);

        router.put("/" + endpoint).handler(this::validate).handler(this::update);
    }

    @Override
    protected void validate(RoutingContext routingContext)
    {
        try
        {
            var item = routingContext.body().asJsonObject();

            if (PortUtil.isConnected("localhost", item.getInteger(FLOW_SETTINGS_SFLOW_PORT)) || PortUtil.isConnected("localhost", item.getInteger(FLOW_SETTINGS_NETFLOW_PORT)) || PortUtil.isConnected("localhost", item.getInteger(FLOW_SETTINGS_BGP_SFLOW_PORT)) || PortUtil.isConnected("localhost", item.getInteger(FLOW_SETTINGS_BGP_NETFLOW_PORT)))
            {
                RequestValidator.sendResponse(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST)
                        .put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL)
                        .put(GlobalConstants.MESSAGE, String.format(InfoMessageConstants.PORT_IS_ALREADY_IN_USE, item.getInteger(FLOW_SETTINGS_SFLOW_PORT) + ", " + item.getInteger(FLOW_SETTINGS_NETFLOW_PORT) + ", " + item.getInteger(FLOW_SETTINGS_BGP_SFLOW_PORT) + ", " + item.getInteger(FLOW_SETTINGS_BGP_NETFLOW_PORT))));
            }
            else
            {
                routingContext.next();
            }
        }
        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }
    }

    @Override
    protected void get(RoutingContext routingContext)
    {
        try
        {
            this.beforeGet().compose(handler ->

                    Future.<JsonObject>future(promise ->
                    {
                        var item = this.configStore.getItem();

                        if (item != null && !item.isEmpty())
                        {
                            promise.complete(CommonUtil.removeSensitiveFields(item, true));
                        }
                        else
                        {
                            promise.complete(new JsonObject());
                        }

                    }).compose(entity -> this.afterGet(entity, routingContext)));
        }
        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }
    }

    @Override
    protected Future<JsonObject> beforeUpdate(RoutingContext routingContext)
    {
        var body = routingContext.body().asJsonObject();

        var item = this.configStore.getItem();

        routingContext.request().params().add("restart.required", !item.getLong(FLOW_SETTINGS_SFLOW_PORT).equals(body.getLong(FLOW_SETTINGS_SFLOW_PORT)) ||
                !item.getLong(FLOW_SETTINGS_NETFLOW_PORT).equals(body.getLong(FLOW_SETTINGS_NETFLOW_PORT)) ||
                !item.getLong(FLOW_SETTINGS_AGGREGATION_INTERVAL_MINUTES).equals(body.getLong(FLOW_SETTINGS_AGGREGATION_INTERVAL_MINUTES)) ||
                !item.getString(FLOW_SETTINGS_BGP_FLOW_ENABLED, NO).equals(body.getString(FLOW_SETTINGS_BGP_FLOW_ENABLED, NO)) ||
                !item.getLong(FLOW_SETTINGS_BGP_SFLOW_PORT, 0L).equals(body.getLong(FLOW_SETTINGS_BGP_SFLOW_PORT, 0L)) ||
                !item.getLong(FLOW_SETTINGS_BGP_NETFLOW_PORT, 0L).equals(body.getLong(FLOW_SETTINGS_BGP_NETFLOW_PORT, 0L)) ? YES : NO);

        routingContext.request().params().add(GlobalConstants.ID, Long.toString(DEFAULT_ID));

        return super.beforeUpdate(routingContext);
    }

    @Override
    protected Future<JsonObject> afterUpdate(JsonObject entity, RoutingContext routingContext)
    {
        if (YES.equalsIgnoreCase(routingContext.request().getParam("restart.required")) && LicenseUtil.FLOW_ENABLED.get())
        {
            Bootstrap.restartVerticle(FlowListener.class).onComplete(result ->
            {
                if (result.succeeded())
                {
                    Bootstrap.vertx().eventBus().send(EVENT_USER_NOTIFICATION,
                            new JsonObject().put(EVENT_TYPE, EVENT_RESTART_ENGINE)
                                    .put(MESSAGE, String.format(InfoMessageConstants.MOTADATA_ENGINE_RESTART_SUCCEEDED, GlobalConstants.MOTADATA_FLOW))
                                    .put(STATUS, STATUS_SUCCEED));
                }
                else
                {
                    Bootstrap.vertx().eventBus().send(EVENT_USER_NOTIFICATION,
                            new JsonObject().put(EVENT_TYPE, EVENT_RESTART_ENGINE)
                                    .put(MESSAGE, String.format(ErrorMessageConstants.MOTADATA_ENGINE_RESTART_FAILED, GlobalConstants.MOTADATA_FLOW))
                                    .put(STATUS, STATUS_FAIL));
                }

                EventBusConstants.publish(EventBusConstants.UI_NOTIFICATION_FLOW_SETTINGS, entity);
            });
        }
        else
        {
            EventBusConstants.publish(EventBusConstants.UI_NOTIFICATION_FLOW_SETTINGS, entity);
        }

        return super.afterUpdate(entity, routingContext);
    }
}
