/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 *	Change Logs:
 *	Date			Author			        Notes
 *  21-Apr-2025     <PERSON><PERSON>        Added Support for Batching Requests
 */

package com.mindarray.api;

import com.mindarray.*;
import com.mindarray.db.ConfigDBConstants;
import com.mindarray.job.JobScheduler;
import com.mindarray.nms.NMSConstants;
import com.mindarray.store.ReportCacheStore;
import com.mindarray.store.ReportConfigStore;
import com.mindarray.store.SchedulerConfigStore;
import com.mindarray.store.UserConfigStore;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import io.vertx.core.Future;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.Router;
import io.vertx.ext.web.RoutingContext;
import org.apache.http.HttpStatus;

import java.util.Set;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.APIConstants.REQUEST_DELETE;
import static com.mindarray.api.APIConstants.RESPONSE_CODE;

public class Report extends AbstractAPI
{
    public static final Logger logger = new Logger(Report.class, GlobalConstants.MOTADATA_API, "Report API");

    public static final String REPORT_NAME = "report.name";

    public static final String REPORT_TYPE = "report.type";

    public static final String REPORT_SCHEDULER = "report.scheduler";

    public static final String REPORT_WIDGETS = "report.widgets";

    public static final String REPORT_CONTEXT = "report.context";

    public static final String REPORT_USER = "report.user";

    public static final String REPORT_FORMAT = "report.format";

    public Report()
    {
        super("reports", ReportConfigStore.getStore(), logger);
    }

    @Override
    public void init(Router router)
    {
        super.init(router, Set.of(REQUEST_DELETE));

        router.put("/" + endpoint + "/:id/schedule").handler(this::schedule); // update report.scheduler -> Yes

        router.delete("/" + endpoint + "/:id").handler(this::validateDelete).handler(this::delete);
    }

    @Override
    protected void get(RoutingContext routingContext)
    {
        try
        {
            var id = CommonUtil.getLong(routingContext.request().getParam(GlobalConstants.ID));

            var item = this.configStore.getItem(id);

            if (item != null && !item.isEmpty())
            {
                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK)
                        .put(GlobalConstants.STATUS, STATUS_SUCCEED)
                        .put(GlobalConstants.RESULT, item.put(PROGRESS, ReportCacheStore.getStore().getItem(item.getLong(GlobalConstants.ID)))));
            }
            else
            {
                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_NOT_FOUND).put(GlobalConstants.STATUS, STATUS_SUCCEED).put(MESSAGE, "Report not found"));
            }
        }
        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }
    }

    @Override
    protected void getAll(RoutingContext routingContext)
    {
        try
        {
            var entities = new JsonArray();

            var items = this.configStore.getItems();

            for (var index = 0; index < items.size(); index++)
            {
                var item = items.getJsonObject(index);

                item.remove(Report.REPORT_WIDGETS);//as not required need to remove it as it unnecessary causes API to be heavy in terms of data sending to UI

                item.put(PROGRESS, ReportCacheStore.getStore().getItem(item.getLong(GlobalConstants.ID)));

                entities.add(item);
            }

            this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK)
                    .put(GlobalConstants.STATUS, STATUS_SUCCEED)
                    .put(GlobalConstants.RESULT, entities));

        }
        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }
    }

    @Override
    protected Future<JsonObject> beforeCreate(RoutingContext routingContext)
    {
        routingContext.body().asJsonObject().put(REPORT_USER, UserConfigStore.getStore().getItemByValue(User.USER_NAME, routingContext.user().principal().getString(User.USER_NAME)).getLong(ID));

        return Future.succeededFuture(routingContext.body().asJsonObject());
    }

    private void validateDelete(RoutingContext routingContext)
    {
        var id = CommonUtil.getLong(routingContext.request().getParam(GlobalConstants.ID));

        if (ReportCacheStore.getStore().isRunning(id))
        {
            RequestValidator.sendResponse(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST)
                    .put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL)
                    .put(GlobalConstants.MESSAGE, String.format(ErrorMessageConstants.FAILED_TO_DELETE_REPORT, "export job is running...")));
        }
        else
        {
            routingContext.next();
        }
    }

    @Override
    protected Future<Void> afterDelete(JsonObject entity, RoutingContext routingContext)
    {
        JobScheduler.deleteSchedulers(entity, JobScheduler.JobType.REPORT.getName());

        return super.afterDelete(entity, routingContext);
    }

    private void schedule(RoutingContext routingContext)
    {
        try
        {
            var id = CommonUtil.getLong(routingContext.request().getParam(GlobalConstants.ID));

            if (ReportConfigStore.getStore().existItem(id))
            {
                var items = SchedulerConfigStore.getStore().getItems(Scheduler.SCHEDULER_CONTEXT, NMSConstants.OBJECTS, new JsonArray().add(id), JobScheduler.JobType.REPORT.getName());

                var scheduled = NO;

                if (items != null && !items.isEmpty())
                {
                    for (var i = 0; i < items.size(); i++)
                    {
                        if (SchedulerConfigStore.getStore().getItem(items.getLong(i)).getString(Scheduler.SCHEDULER_STATE).equals(YES))
                        {
                            scheduled = YES;

                            break;
                        }
                    }
                }

                Bootstrap.configDBService().update(ConfigDBConstants.COLLECTION_REPORT,
                        new JsonObject().put(ConfigDBConstants.FIELD_NAME, GlobalConstants.ID).put(VALUE, id),
                        new JsonObject().put(REPORT_SCHEDULER, scheduled),
                        routingContext.user().principal().getString(User.USER_NAME),
                        routingContext.request().remoteAddress().host(),
                        asyncResult ->
                        {
                            if (asyncResult.succeeded())
                            {
                                ReportConfigStore.getStore().updateItem(id).onComplete(result ->
                                        this.send(routingContext, new JsonObject().put(GlobalConstants.STATUS, STATUS_SUCCEED).put(RESPONSE_CODE, HttpStatus.SC_OK)
                                                .put(GlobalConstants.MESSAGE, String.format(InfoMessageConstants.ENTITY_UPDATED, "Report"))));
                            }
                            else
                            {
                                this.send(routingContext, new JsonObject().put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL).put(RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST)
                                        .put(GlobalConstants.ERROR, asyncResult.failed() ? CommonUtil.formatStackTrace(asyncResult.cause().getStackTrace()) : UNKNOWN)
                                        .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                                        .put(GlobalConstants.MESSAGE, String.format(ErrorMessageConstants.ENTITY_UPDATE_FAILED, "Report", asyncResult.cause().getMessage())));
                            }
                        });
            }
            else
            {
                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST)
                        .put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL)
                        .put(GlobalConstants.MESSAGE, ErrorMessageConstants.API_INVALID_INPUT_PARAMETERS));
            }
        }
        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }
    }
}
