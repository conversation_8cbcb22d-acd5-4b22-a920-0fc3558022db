/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.api;

import com.mindarray.ErrorCodes;
import com.mindarray.ErrorMessageConstants;
import com.mindarray.GlobalConstants;
import com.mindarray.store.SNMPTrapProfileConfigStore;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.Router;
import io.vertx.ext.web.RoutingContext;
import org.apache.http.HttpStatus;

import static com.mindarray.GlobalConstants.*;

public class SNMPTrapProfile extends AbstractAPI
{
    public static final String SNMP_TRAP_PROFILE_OID = "snmp.trap.profile.oid";
    public static final String SNMP_TRAP_PROFILE_NAME = "snmp.trap.profile.name";
    public static final String SNMP_TRAP_PROFILE_AUTO_CLEAR_OID = "snmp.trap.profile.auto.clear.oid";
    public static final String SNMP_TRAP_PROFILE_DROP_STATUS = "snmp.trap.profile.drop.status";
    public static final String SNMP_TRAP_PROFILE_AUTO_CLEAR_STATUS = "snmp.trap.profile.auto.clear.status";
    public static final String SNMP_TRAP_PROFILE_AUTO_CLEAR_TIMER = "snmp.trap.profile.auto.clear.timer";
    public static final String SNMP_TRAP_PROFILE_SEVERITY = "snmp.trap.profile.severity";
    public static final String SNMP_TRAP_PROFILE_TRANSLATOR = "snmp.trap.profile.translator";
    private static final Logger LOGGER = new Logger(SNMPTrapProfile.class, MOTADATA_API, "SNMP Trap Profile API");

    public SNMPTrapProfile()
    {
        super("snmp-trap-profiles", SNMPTrapProfileConfigStore.getStore(), LOGGER);
    }

    @Override
    public void init(Router router)
    {
        try
        {
            super.init(router);

            router.get("/" + endpoint + "/:id/references").handler(this::getReferences);
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    @Override
    protected Future<JsonObject> beforeCreate(RoutingContext routingContext)
    {
        return distinct(routingContext, APIConstants.REQUEST_CREATE);
    }

    @Override
    protected Future<JsonObject> beforeUpdate(RoutingContext routingContext)
    {
        return distinct(routingContext, APIConstants.REQUEST_UPDATE);
    }

    private Future<JsonObject> distinct(RoutingContext routingContext, String requestType)
    {
        var promise = Promise.<JsonObject>promise();

        var requestParameters = routingContext.body().asJsonObject();

        var item = this.configStore.getItemByValue(SNMP_TRAP_PROFILE_NAME, requestParameters.getString(SNMP_TRAP_PROFILE_NAME));

        var exist = false;

        if (item != null)
        {
            exist = !requestType.equalsIgnoreCase(APIConstants.REQUEST_UPDATE) || !item.getLong(ID).equals(CommonUtil.getLong(routingContext.request().getParam(ID)));
        }

        if (exist)
        {
            this.send(routingContext, new JsonObject().put(APIConstants.RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST).put(ERROR_CODE, ErrorCodes.ERROR_CODE_BAD_REQUEST)
                    .put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL).put(GlobalConstants.MESSAGE, String.format(ErrorMessageConstants.API_FIELD_UNIQUE_RULE, SNMP_TRAP_PROFILE_NAME)));

            promise.fail(String.format(ErrorMessageConstants.API_FIELD_UNIQUE_RULE, SNMP_TRAP_PROFILE_NAME));
        }
        else
        {
            promise.complete(requestParameters);
        }

        return promise.future();
    }
}
