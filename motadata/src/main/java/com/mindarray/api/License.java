/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.api;

import com.mindarray.Bootstrap;
import com.mindarray.ErrorCodes;
import com.mindarray.ErrorMessageConstants;
import com.mindarray.GlobalConstants;
import com.mindarray.util.CodecUtil;
import com.mindarray.util.LicenseUtil;
import com.mindarray.util.Logger;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.Router;
import io.vertx.ext.web.RoutingContext;
import org.apache.http.HttpStatus;

import java.util.Set;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.InfoMessageConstants.RESTART_REQUIRED_TO_UPDATE_LICENSE;
import static com.mindarray.api.APIConstants.*;
import static com.mindarray.eventbus.EventBusConstants.*;

public class License extends AbstractAPI
{
    public static final String LICENSE_ACTIVATION_CODE = "license.activation.code";
    private static final Logger LOGGER = new Logger(License.class, MOTADATA_API, "License API");

    public License()
    {
        super("license", null, LOGGER);
    }

    @Override
    public void init(Router router)
    {
        super.init(router, Set.of(
                REQUEST_GET,
                REQUEST_UPDATE,
                REQUEST_CREATE,
                REQUEST_DELETE
        ));

        router.get("/" + endpoint).handler(this::getAll);

        router.put("/" + endpoint + "/:id").handler(this::validate).handler(this::update);
    }

    @Override
    protected void update(RoutingContext routingContext)
    {
        try
        {
            var licenseCode = routingContext.body().asJsonObject().getString(LICENSE_ACTIVATION_CODE);

            LicenseUtil.validate(false, Buffer.buffer(licenseCode)).onComplete(result ->
            {
                try
                {
                    if (result.succeeded())
                    {
                        LOGGER.info(String.format("updating license %s", licenseCode));

                        // Write License
                        Bootstrap.vertx().fileSystem().writeFileBlocking(LicenseUtil.LICENSE_FILE, Buffer.buffer(licenseCode));

                        // Check for upgrade logic
                        //if license is updated it needs to restart everytime.
                        var license = result.result();


                        if (Bootstrap.vertx().fileSystem().existsBlocking(LicenseUtil.LICENSE_PATH))
                        {
                            var buffer = Bootstrap.vertx().fileSystem().readFileBlocking(LicenseUtil.LICENSE_PATH);

                            license.mergeIn(new JsonObject(new String(CodecUtil.toBytes(buffer.getBytes()))));
                        }

                        LicenseUtil.load(license);

                        LOGGER.warn(RESTART_REQUIRED_TO_UPDATE_LICENSE);

                        Bootstrap.vertx().eventBus().send(EVENT_AGENT, new JsonObject().put(EVENT, license).put(EVENT_TYPE, EVENT_LICENSE_UPDATE));

                        this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK)
                                .put(GlobalConstants.STATUS, STATUS_SUCCEED)
                                .put(MESSAGE, RESTART_REQUIRED_TO_UPDATE_LICENSE));


                        LOGGER.info(String.format("license %s updated successfully...", licenseCode));
                    }
                    else
                    {
                        LOGGER.fatal(String.format("license validation failed, reason : %s", result.cause().getMessage()));

                        this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST).put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR).put(GlobalConstants.STATUS, STATUS_FAIL)
                                .put(MESSAGE, String.format(ErrorMessageConstants.ENTITY_UPDATE_FAILED, "License", result.cause().getMessage())));

                    }
                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);
                }
            });
        }
        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }
    }

    @Override
    protected void getAll(RoutingContext routingContext)
    {
        try
        {
            var item = LicenseUtil.getLicenseDetails();

            if (item != null)
            {
                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, STATUS_SUCCEED).put(ID, routingContext.user().principal().getLong(ID)).put(GlobalConstants.RESULT, item));
            }
            else
            {
                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST).put(ERROR_CODE, ErrorCodes.ERROR_CODE_BAD_REQUEST).put(GlobalConstants.STATUS, STATUS_FAIL).put(MESSAGE, ErrorMessageConstants.LICENSE_DETAILS_NOT_FOUND));
            }
        }
        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }
    }
}
