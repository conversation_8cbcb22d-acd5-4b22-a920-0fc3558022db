/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */
package com.mindarray.api;

import com.mindarray.ErrorCodes;
import com.mindarray.ErrorMessageConstants;
import com.mindarray.GlobalConstants;
import com.mindarray.store.SingleSignOnConfigStore;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import com.mindarray.util.SingleSignOnUtil;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.Router;
import io.vertx.ext.web.RoutingContext;
import org.apache.http.HttpStatus;

import java.util.Set;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.APIConstants.*;
import static org.apache.http.HttpStatus.SC_BAD_REQUEST;

public class SingleSignOn extends AbstractAPI
{

    public static final String SAML_AUTHENTICATION_PROTOCOL = "saml.authentication.protocol";
    public static final String SAML_AUTHENTICATION_CONTEXT = "saml.authentication.context";
    public static final String SAML_IDENTITY_PROVIDER_ENTITY_ID = "saml.identity.provider.entity.id";
    public static final String SAML_IDENTITY_PROVIDER_LOGIN_URL = "saml.identity.provider.login.url";
    public static final String SAML_IDENTITY_PROVIDER = "saml.identity.provider";
    public static final String SAML_IDENTITY_PROVIDER_FINGERPRINT = "saml.identity.provider.fingerprint";
    public static final String SAML_IDENTITY_PROVIDER_METADATA_FILE = "saml.identity.provider.metadata.file";
    public static final String SAML_IDENTITY_PROVIDER_CONFIGURATION = "saml.identity.provider.configuration";
    public static final String SAML_IDENTITY_PROVIDER_CERTIFICATE_CONFIGURATION = "saml.identity.provider.certificate.configuration";
    public static final String SAML_IDENTITY_PROVIDER_CERTIFICATE = "saml.identity.provider.certificate";
    public static final String SAML_SERVICE_PROVIDER_LOGIN_URL = "saml.service.provider.login.url";
    public static final String SAML_SERVICE_PROVIDER_LOGOUT_URL = "saml.service.provider.logout.url";
    public static final String SAML_CONSUMER_SERVICE_URL = "saml.consumer.service.url";
    public static final String SAML_SERVICE_PROVIDER_ENTITY_ID = "saml.service.provider.entity.id";
    public static final String SAML_FILE_BASED_AUTHENTICATION = "saml.file.based.authentication";
    public static final String SAML_CONFIGURATION_FILE = "saml.configuration.file";
    public static final String SAML_2 = "SAML2.0";
    private static final Logger LOGGER = new Logger(SingleSignOn.class, MOTADATA_API, "Single Sign On");

    public SingleSignOn()
    {
        super("single-sign-on", SingleSignOnConfigStore.getStore(), new Logger(SingleSignOn.class, MOTADATA_API, "Single Sign On API"));
    }

    @Override
    public void init(Router router)
    {
        try
        {
            super.init(router, Set.of(REQUEST_CREATE, REQUEST_DELETE));
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    @Override
    protected Future<JsonObject> beforeUpdate(RoutingContext routingContext)
    {
        var promise = Promise.<JsonObject>promise();

        var params = routingContext.body().asJsonObject();

        if (params.containsKey(SAML_AUTHENTICATION_PROTOCOL) && CommonUtil.isNotNullOrEmpty(params.getString(SAML_AUTHENTICATION_PROTOCOL))
                && params.containsKey(SAML_AUTHENTICATION_CONTEXT) && CommonUtil.isNotNullOrEmpty(params.getString(SAML_AUTHENTICATION_CONTEXT)))
        {
            if (params.getString(SAML_AUTHENTICATION_PROTOCOL).equals(SAML_2))
            {
                var authenticationContext = params.getJsonObject(SAML_AUTHENTICATION_CONTEXT);

                if (authenticationContext.getString(SingleSignOn.SAML_IDENTITY_PROVIDER_CONFIGURATION).equalsIgnoreCase("upload.metadata.file"))
                {
                    var context = new JsonObject();

                    context.put(SAML_FILE_BASED_AUTHENTICATION, true);

                    context.put(SAML_CONFIGURATION_FILE, GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + GlobalConstants.UPLOADS +
                            GlobalConstants.PATH_SEPARATOR + authenticationContext.getJsonArray(SingleSignOn.SAML_IDENTITY_PROVIDER_METADATA_FILE).getJsonObject(0).getString(RESULT));

                    context.put(SAML_SERVICE_PROVIDER_ENTITY_ID, "Motadata-AIOps")
                            .put(SAML_IDENTITY_PROVIDER, authenticationContext.getString(SAML_IDENTITY_PROVIDER))
                            .put(SAML_IDENTITY_PROVIDER_CONFIGURATION, "upload.metadata.file")
                            .put(SAML_IDENTITY_PROVIDER_METADATA_FILE, authenticationContext.getJsonArray(SAML_IDENTITY_PROVIDER_METADATA_FILE));

                    params.put(SAML_AUTHENTICATION_CONTEXT, context);
                }
                else
                {
                    authenticationContext.put(SAML_FILE_BASED_AUTHENTICATION, false);

                    var context = SingleSignOnConfigStore.getStore().getItem().getJsonObject(SAML_AUTHENTICATION_CONTEXT);

                    if (authenticationContext.containsKey(SAML_IDENTITY_PROVIDER_CERTIFICATE_CONFIGURATION)
                            && authenticationContext.getString(SingleSignOn.SAML_IDENTITY_PROVIDER_CERTIFICATE_CONFIGURATION).equalsIgnoreCase("configure.manually.fingerprint"))
                    {
                        if (context.containsKey(SAML_IDENTITY_PROVIDER_CERTIFICATE))
                        {
                            context.remove(SAML_IDENTITY_PROVIDER_CERTIFICATE);

                            authenticationContext.remove(SAML_IDENTITY_PROVIDER_CERTIFICATE);
                        }

                    }
                    else if (context.containsKey(SAML_IDENTITY_PROVIDER_FINGERPRINT))
                    {
                        context.remove(SAML_IDENTITY_PROVIDER_FINGERPRINT);

                        authenticationContext.remove(SAML_IDENTITY_PROVIDER_FINGERPRINT);
                    }
                }

                promise.complete(params);
            }
            else
            {
                send(routingContext, new JsonObject().put(STATUS, STATUS_FAIL).put(ERROR_CODE, ErrorCodes.ERROR_CODE_BAD_REQUEST)
                        .put(RESPONSE_CODE, SC_BAD_REQUEST).put(MESSAGE, (ErrorMessageConstants.INVALID_AUTHENTICATION_PROTOCOL)));

                promise.fail(ErrorMessageConstants.INVALID_AUTHENTICATION_PROTOCOL);
            }
        }
        else
        {
            send(routingContext, new JsonObject().put(STATUS, STATUS_FAIL).put(ERROR_CODE, ErrorCodes.ERROR_CODE_BAD_REQUEST)
                    .put(RESPONSE_CODE, SC_BAD_REQUEST).put(MESSAGE, (ErrorMessageConstants.MISSING_AUTHENTICATION_PROTOCOL_AUTHENTICATION_CONTEXT)));

            promise.fail(ErrorMessageConstants.MISSING_AUTHENTICATION_PROTOCOL_AUTHENTICATION_CONTEXT);
        }

        return promise.future();
    }

    @Override
    protected Future<JsonObject> afterUpdate(JsonObject entity, RoutingContext routingContext)
    {
        SingleSignOnUtil.init();

        return super.afterUpdate(entity, routingContext);
    }

    @Override
    protected Future<Void> afterGetAll(JsonArray entities, RoutingContext routingContext)
    {

        var url = SingleSignOnUtil.getServerURL();

        entities.getJsonObject(0).getJsonObject(SAML_AUTHENTICATION_CONTEXT)
                .put(SAML_SERVICE_PROVIDER_LOGIN_URL, url + "/api/v1/sso")
                .put(SAML_CONSUMER_SERVICE_URL, url + "/api/v1/sso/callback")
                .put(SAML_SERVICE_PROVIDER_LOGOUT_URL, url + "/api/v1/sso/logout");

        this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, STATUS_SUCCEED).put(GlobalConstants.RESULT, entities));

        return Future.succeededFuture();
    }

}
