/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.store;

import com.mindarray.GlobalConstants;
import com.mindarray.api.Configuration;
import com.mindarray.api.Discovery;
import com.mindarray.api.MailServerConfiguration;
import com.mindarray.db.ConfigDBConstants;
import com.mindarray.util.Logger;
import io.vertx.core.json.JsonObject;

import java.util.List;
import java.util.Map;

import static com.mindarray.api.APIConstants.*;

public class CredentialProfileConfigStore extends AbstractConfigStore
{

    private static final Logger LOGGER = new Logger(CredentialProfileConfigStore.class, GlobalConstants.MOTADATA_STORE, "Credential Profile Config Store");

    private static final CredentialProfileConfigStore STORE = new CredentialProfileConfigStore();

    private CredentialProfileConfigStore()
    {

        super(ConfigDBConstants.COLLECTION_CREDENTIAL_PROFILE, LOGGER, false,
                List.of(
                        Map.of(
                                REFERENCE_ENTITY, Entity.DISCOVERY,
                                REFERENCE_ENTITY_PROPERTY, Discovery.DISCOVERY_CREDENTIAL_PROFILES,
                                REFERENCE_ENTITY_STORE, ConfigStore.DISCOVERY,
                                REFERENCE_ENTITY_PROPERTY_TYPE, ReferenceEntityPropertyType.MULTI_VALUE
                        ),
                        Map.of(
                                REFERENCE_ENTITY, Entity.CONFIGURATION,
                                REFERENCE_ENTITY_PROPERTY, Configuration.CONFIG_CREDENTIAL_PROFILE,
                                REFERENCE_ENTITY_STORE, ConfigStore.CONFIGURATION,
                                REFERENCE_ENTITY_PROPERTY_TYPE, ReferenceEntityPropertyType.VALUE
                        ),
                        Map.of(
                                REFERENCE_ENTITY, Entity.MAIL_SERVER_CONFIGURATION,
                                REFERENCE_ENTITY_PROPERTY, MailServerConfiguration.MAIL_SERVER_CREDENTIAL_PROFILE,
                                REFERENCE_ENTITY_STORE, ConfigStore.MAIL_SERVER,
                                REFERENCE_ENTITY_PROPERTY_TYPE, ReferenceEntityPropertyType.VALUE
                        )
                )
        );
    }

    public static CredentialProfileConfigStore getStore()
    {
        return STORE;
    }

    @Override
    public JsonObject getItem(long id)
    {
        return id == GlobalConstants.NOT_AVAILABLE ? new JsonObject() : super.getItem(id);
    }
}
