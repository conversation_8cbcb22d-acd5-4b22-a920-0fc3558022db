/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 *     Change Logs:
 *     Date            Author       Notes
 *     15-Apr-2025      Bharat      MOTADATA-5822: Metric Explorer Enhancements | Initial Version
 */

package com.mindarray.store;

import com.mindarray.GlobalConstants;
import com.mindarray.api.MetricExplorer;
import com.mindarray.db.ConfigDBConstants;
import com.mindarray.util.Logger;
import io.vertx.core.json.JsonArray;

import static com.mindarray.GlobalConstants.NO;
import static com.mindarray.GlobalConstants.NOT_AVAILABLE;

public class MetricExplorerConfigStore extends AbstractConfigStore
{

    private static final Logger LOGGER = new Logger(MetricExplorerConfigStore.class, GlobalConstants.MOTADATA_STORE, "Metric Explorer Config Store");

    private static final MetricExplorerConfigStore STORE = new MetricExplorerConfigStore();

    private MetricExplorerConfigStore()
    {

        super(ConfigDBConstants.COLLECTION_METRIC_EXPLORER, LOGGER, false);
    }

    public static MetricExplorerConfigStore getStore()
    {
        return STORE;
    }


    /**
     * Retrieves metric explorer configurations specific to a monitor based on its ID and type.
     * This method filters through the stored metric explorer configurations and returns those that:
     * 1. Have global view disabled (NO)
     * 2. Match the specified object type
     * 3. Are either applied to the specific object ID or applied by type
     *
     * @param id   The ID of the monitor for which to retrieve metric explorer
     * @param type The type of the monitor (e.g., "server", "database", etc.)
     */
    public JsonArray getItems(long id, String type)
    {
        var items = new JsonArray();

        for (var entry : this.items.entrySet())
        {
            var item = entry.getValue();

            if (NO.equalsIgnoreCase(item.getString(MetricExplorer.METRIC_EXPLORER_GLOBAL_VIEW_ENABLED))
                    && item.containsKey(MetricExplorer.METRIC_EXPLORER_OBJECT_TYPE)
                    && item.getString(MetricExplorer.METRIC_EXPLORER_OBJECT_TYPE).equalsIgnoreCase(type))
            {
                if (item.getLong(MetricExplorer.METRIC_EXPLORER_OBJECT_ID) == id || item.getLong(MetricExplorer.METRIC_EXPLORER_OBJECT_ID) == NOT_AVAILABLE)
                {
                    items.add(item);
                }
            }
        }

        return items;
    }
}
