/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 *  Change Logs:
 *  Date            Author          Notes
 *  3-Mar-2025      Chandresh       Initial Version
 */

package com.mindarray.patch;

import com.mindarray.Bootstrap;
import com.mindarray.api.LDAPServer;
import com.mindarray.store.LDAPServerConfigStore;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonObject;

import java.util.ArrayList;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.db.ConfigDBConstants.COLLECTION_LDAP_SERVER;
import static com.mindarray.db.ConfigDBConstants.FIELD_NAME;

public class Patch8017 implements Patch
{
    private static final Logger LOGGER = new Logger(Patch8017.class, MOTADATA_PATCH, "Patch 8.0.17");

    private static final String VERSION = "8.0.17";

    @Override
    public String getVersion()
    {
        return VERSION;
    }

    @Override
    public Future<Void> doPatch()
    {
        var promise = Promise.<Void>promise();

        var futures = new ArrayList<Future<Void>>();

        LOGGER.info("executing patch 8.0.17");

        futures.add(executeLDAPServerPatch());

        Future.join(futures).onComplete(result ->
        {
            if (result.succeeded())
            {
                promise.complete();

                LOGGER.info("successfully executed patch 8.0.17");
            }
            else
            {
                promise.fail(result.cause());

                LOGGER.error(result.cause());
            }
        });

        return promise.future();
    }

    private Future<Void> executeLDAPServerPatch()
    {
        var promise = Promise.<Void>promise();

        try
        {
            var items = LDAPServerConfigStore.getStore().getItems();

            var futures = new ArrayList<Future<Void>>();

            for (var index = 0; index < items.size(); index++)
            {
                var item = items.getJsonObject(index);

                if (item.containsKey("ldap.server.host") && CommonUtil.isNotNullOrEmpty(item.getString("ldap.server.host")))
                {
                    var future = Promise.<Void>promise();

                    futures.add(future.future());

                    item.put(LDAPServer.LDAP_SERVER_PRIMARY_HOST, item.getString("ldap.server.host"));

                    Bootstrap.configDBService().update(COLLECTION_LDAP_SERVER,
                            new JsonObject().put(FIELD_NAME, ID).put(VALUE, item.getLong(ID)), item,
                            DEFAULT_USER, MOTADATA_SYSTEM, result ->
                            {
                                if (result.succeeded())
                                {
                                    LDAPServerConfigStore.getStore().updateItem(item.getLong(ID)).onComplete(asyncResult ->
                                    {
                                        if (asyncResult.succeeded())
                                        {
                                            if (CommonUtil.traceEnabled())
                                            {
                                                LOGGER.trace(String.format("updated ldap server : %s ", item.encode()));
                                            }

                                            future.complete();
                                        }
                                        else
                                        {
                                            future.fail(asyncResult.cause());
                                        }
                                    });
                                }
                                else
                                {
                                    LOGGER.error(result.cause());

                                    future.fail(result.cause());
                                }
                            });
                }
            }

            Future.join(futures).onComplete(result ->
            {
                if (result.succeeded())
                {
                    LOGGER.info("LDAP Servers updated successfully!");

                    promise.complete();
                }
                else
                {
                    LOGGER.info("Failed to LDAP Servers : " + result.cause());

                    promise.fail(result.cause());
                }
            });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            promise.fail(exception);
        }

        return promise.future();
    }
}
