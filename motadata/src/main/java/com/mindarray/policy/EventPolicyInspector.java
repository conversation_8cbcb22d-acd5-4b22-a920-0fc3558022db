/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 *   Change Logs:
 *   Date          Author              Notes
 *   2025-02-04    Smit Prajapati      MOTADATA-4954  Alert Sound Notification for Event Policy
 *   2025-02-11    Chandresh           MOTADATA-446   Event Policy Template related enhancements
 *   2025-02-25	   Darshan Parmar	   MOTADATA-5215  SonarQube Suggestions Resolution
 *   2025-02-27    Chopra Deven        MOTADATA-4973   Dumping Event source for Event Policy in case of notification trigger action event and policy trigger event
 *   2025-02-28    Smit Prajapati      MOTADATA-4956   Added try-cache block for exception handling
 *   2025-03-25    Smit Prajapati      MOTADATA-5435  Flow back-pressure mechanism.
 *   9-Apr-2025    Bharat              MOTADATA-5141: Alert Drill-down from email and Teams Notification
 */

package com.mindarray.policy;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.InfoMessageConstants;
import com.mindarray.api.*;
import com.mindarray.datastore.DatastoreConstants;
import com.mindarray.db.ConfigDBConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.eventbus.EventEngine;
import com.mindarray.job.JobScheduler;
import com.mindarray.nms.SNMPTrapProcessor;
import com.mindarray.notification.Notification;
import com.mindarray.store.EventPolicyCacheStore;
import com.mindarray.store.EventPolicyConfigStore;
import com.mindarray.store.IntegrationConfigStore;
import com.mindarray.store.SchedulerConfigStore;
import com.mindarray.util.*;
import com.mindarray.visualization.VisualizationConstants;
import io.vertx.core.AbstractVerticle;
import io.vertx.core.Promise;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.eventbus.DeliveryOptions;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import org.apache.commons.text.StringSubstitutor;

import java.nio.charset.StandardCharsets;
import java.util.*;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.EventPolicy.*;
import static com.mindarray.api.NetRoute.NETROUTE_ID;
import static com.mindarray.api.User.USER_NAME;
import static com.mindarray.eventbus.EventBusConstants.*;
import static com.mindarray.notification.Notification.NOTIFICATION_TYPE;
import static com.mindarray.policy.PolicyEngineConstants.*;

/**
 * EventPolicyInspector is responsible for evaluating events against defined policies.
 * <p>
 * This class:
 * 1. Loads and manages event policies from the configuration store
 * 2. Processes incoming events and evaluates them against policy conditions
 * 3. Handles both in-memory and scheduled policies
 * 4. Supports grouping and aggregation of events
 * 5. Triggers actions (notifications, runbooks) when policy conditions are met
 * 6. Manages policy suppression and evaluation windows
 * <p>
 * The inspector runs as a Vert.x verticle and communicates with other components
 * through the event bus.
 */
public class EventPolicyInspector extends AbstractVerticle
{
    private static final Logger LOGGER = new Logger(EventPolicyInspector.class, GlobalConstants.MOTADATA_POLICY, "Event Policy Inspector");
    private static final DeliveryOptions DELIVERY_OPTIONS = new DeliveryOptions().setSendTimeout(300000L);
    private static final int INTERVAL_SECONDS = MotadataConfigUtil.getEventPolicyInspectionTimerSeconds();
    private static final int TOP_N_GROUPS = MotadataConfigUtil.getEventPolicyTopNGroups();
    private static final int MAX_GROUPS = MotadataConfigUtil.getEventPolicyMaxGroups();
    private static final String LOCAL_HOST = MotadataConfigUtil.getHost();
    private final Map<Long, JsonObject> policies = new HashMap<>(); //policies context
    private final Map<Long, Integer> policyCurrentIndices = new HashMap<>(); //policy current index which has last updated index count
    private final Map<Long, long[]> policySumValues = new HashMap<>();//policy sum context
    private final Map<Long, int[]> policyCountValues = new HashMap<>();//policy count context
    private final Map<Long, String[]> policyGroups = new HashMap<>();//if policy has groupby then will be storing grouping values will be storing max 1000 unique values combination
    private final Map<Long, Map<String, Integer>> policyGroupIndices = new HashMap<>();//if policy has groupby then will be storing grouping values index
    private final Map<Long, Long> policyEvaluationTicks = new HashMap<>();
    private final JsonObject eventColumns = new JsonObject();
    private final Set<Long> disabledPolicyActionTriggers = new HashSet<>();
    private final StringBuilder groupingKeys = new StringBuilder();
    private final Map<Long, String[]> groupingCounters = new HashMap<>();//if policy has groupby then will be storing grouping counters columns
    private final JsonObject record = new JsonObject();
    private final JsonArray records = new JsonArray();
    private final Map<Long, Integer> evaluatedPolicies = new HashMap<>();
    private final StringBuilder builder = new StringBuilder(0);
    private EventEngine eventEngine;
    private Set<String> mappers;

    /**
     * Swaps elements in the arrays used for policy evaluation sorting.
     * This utility method handles the complex swapping of multiple related arrays
     * that need to maintain their relationships during sorting.
     *
     * @param sort         Flag indicating whether to sort by sum values (true) or count values (false)
     * @param sumValues    Array of sum values for policy evaluation
     * @param countValues  Array of count values for policy evaluation
     * @param groups       Array of group identifiers
     * @param groupIndices Map of group identifiers to their indices
     * @param i            Index of the first element to swap
     * @param j            Index of the second element to swap
     */
    static void swap(boolean sort, long[] sumValues, int[] countValues, String[] groups, Map<String, Integer> groupIndices, int i, int j)
    {
        try
        {
            if (sort)
            {
                var value = sumValues[i];

                var count = countValues[i];

                var groupingKey = groups[i];

                sumValues[i] = sumValues[j];

                sumValues[j] = value;

                countValues[i] = countValues[j];

                countValues[j] = count;

                groups[i] = groups[j];

                groups[j] = groupingKey;

                if (groupingKey != null)
                {
                    groupIndices.put(groupingKey, j);
                }
            }
            else
            {
                var count = countValues[i];

                var groupingKey = groups[i];

                countValues[i] = countValues[j];

                countValues[j] = count;

                groups[i] = groups[j];

                if (groups[j] != null)
                {
                    groupIndices.put(groups[j], i);
                }

                groups[j] = groupingKey;

                if (groupingKey != null)
                {
                    groupIndices.put(groupingKey, j);
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    /**
     * Partitions arrays for quicksort algorithm used in policy evaluation.
     * This method is part of the quicksort implementation and handles the
     * partitioning step, maintaining relationships between multiple arrays.
     * It includes an optimization for early termination when all elements are equal.
     *
     * @param sort         Flag indicating whether to sort by sum values (true) or count values (false)
     * @param sumValues    Array of sum values for policy evaluation
     * @param countValues  Array of count values for policy evaluation
     * @param groups       Array of group identifiers
     * @param groupIndices Map of group identifiers to their indices
     * @param low          Starting index for the partition
     * @param high         Ending index for the partition
     * @return The partition index, or -1 if all elements are equal
     */
    static int partition(boolean sort, long[] sumValues, int[] countValues, String[] groups, Map<String, Integer> groupIndices, int low, int high)
    {
        var pivot = sort ? sumValues[high] : countValues[high];

        var i = low - 1;

        var equal = true;

        for (var j = low; j < high; j++)
        {
            if ((sort && sumValues[j] != pivot) || (!sort && countValues[j] != pivot))
            {
                equal = false;
            }

            if ((sort && sumValues[j] >= pivot) || (!sort && countValues[j] >= pivot))
            {
                i++;

                swap(sort, sumValues, countValues, groups, groupIndices, i, j);
            }
        }

        swap(sort, sumValues, countValues, groups, groupIndices, i + 1, high);

        // Early termination if all elements are the same
        if (equal) return -1;

        return i + 1;
    }

    /**
     * Implements quicksort algorithm for policy evaluation data.
     * This recursive method sorts the arrays used in policy evaluation,
     * maintaining relationships between multiple arrays during sorting.
     * It includes an optimization to stop recursion when all elements are identical.
     *
     * @param sort         Flag indicating whether to sort by sum values (true) or count values (false)
     * @param sumValues    Array of sum values for policy evaluation
     * @param countValues  Array of count values for policy evaluation
     * @param groups       Array of group identifiers
     * @param groupIndices Map of group identifiers to their indices
     * @param low          Starting index for the sort
     * @param high         Ending index for the sort
     */
    static void sort(boolean sort, long[] sumValues, int[] countValues, String[] groups, Map<String, Integer> groupIndices, int low, int high)
    {
        if (low < high)
        {
            var pi = partition(sort, sumValues, countValues, groups, groupIndices, low, high);

            // If partition returns -1, it means all elements are identical, so stop recursion
            if (pi == -1) return;

            sort(sort, sumValues, countValues, groups, groupIndices, low, pi - 1);

            sort(sort, sumValues, countValues, groups, groupIndices, pi + 1, high);
        }
    }

    /**
     * Initializes the EventPolicyInspector verticle.
     * This method:
     * 1. Loads event policies from the configuration store
     * 2. Sets up event bus handlers for policy management and events
     * 3. Initializes periodic timers for policy evaluation
     * 4. Registers with the event engine for event processing
     *
     * @param promise Promise to be completed when initialization is done
     * @throws Exception If an error occurs during initialization
     */
    @Override
    public void start(Promise<Void> promise) throws Exception
    {
        mappers = new HashSet<>();

        vertx.eventBus().<JsonObject>request(EventBusConstants.EVENT_EVENT_COLUMN_MAPPER_QUERY, EMPTY_VALUE, reply ->
        {
            eventColumns.mergeIn(reply.result().body().getJsonObject(DatastoreConstants.EVENT_COLUMNS));

            try
            {
                var items = EventPolicyConfigStore.getStore().getItems();

                for (var index = 0; index < items.size(); index++)
                {
                    var policy = items.getJsonObject(index);

                    if (!policy.containsKey(POLICY_ARCHIVED) || policy.getString(POLICY_ARCHIVED).equalsIgnoreCase(NO))
                    {
                        policies.put(policy.getLong(ID), enrich(policy));
                    }
                }

                init();
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);
            }
        });

        vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_COLUMN_MAPPER_UPDATE, message ->
        {
            try
            {
                var event = message.body();

                var tokens = event.getString(DatastoreConstants.MAPPER).split(GlobalConstants.COLUMN_SEPARATOR, -1);

                if (event.getString(CHANGE_NOTIFICATION_TYPE).equalsIgnoreCase(EventBusConstants.ChangeNotificationType.UPDATE_EVENT_COLUMN.name()))
                {
                    update(eventColumns, tokens, false);
                }
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);
            }
        }).exceptionHandler(LOGGER::error);

        vertx.setPeriodic(INTERVAL_SECONDS * 1000L, timer ->
        {
            for (var entry : evaluatedPolicies.entrySet())
            {
                var policy = policies.get(entry.getKey());

                var interval = entry.getValue() - INTERVAL_SECONDS;

                if (interval <= 0)
                {
                    var context = policy.getJsonObject(PolicyEngineConstants.POLICY_CONTEXT);

                    entry.setValue(context.getString(EVALUATION_WINDOW_UNIT).equalsIgnoreCase(DateTimeUtil.SECOND) ? context.getInteger(EVALUATION_WINDOW) : context.getInteger(EventPolicy.EVALUATION_WINDOW) * 60);

                    inspect(new JsonObject().put("inspect", YES).put(PolicyEngineConstants.POLICY_ID, entry.getKey()));
                }
                else
                {
                    entry.setValue(interval);
                }
            }
        });

        eventEngine = new EventEngine().setEventType(config().getString(EventBusConstants.EVENT_TYPE))
                .setPersistEventOffset(true).setLogger(LOGGER).setEventHandler(event ->
                {

                    if (event.containsKey(RESULT) || event.containsKey("schedule"))
                    {
                        inspect(event, event.getLong(POLICY_ID));
                    }
                    else
                    {
                        inspect(event);
                    }
                }).start(vertx, promise);

        vertx.eventBus().<JsonObject>localConsumer(EVENT_CHANGE_NOTIFICATION, message ->
        {
            try
            {
                var event = message.body();

                var notificationType = EventBusConstants.ChangeNotificationType.valueOf(event.getString(CHANGE_NOTIFICATION_TYPE));

                switch (notificationType)
                {

                    case ADD_POLICY ->
                    {
                        var policy = EventPolicyConfigStore.getStore().getItem(event.getLong(ID));

                        policies.put(policy.getLong(ID), enrich(policy));

                        disabledPolicyActionTriggers.remove(event.getLong(ID));

                        init(policy, policy.getLong(ID));
                    }

                    case UPDATE_POLICY ->
                    {
                        var policy = EventPolicyConfigStore.getStore().getItem(event.getLong(ID));

                        policies.put(policy.getLong(ID), enrich(policy));

                        init(policy, policy.getLong(ID));
                    }

                    case DELETE_POLICY ->
                    {
                        policies.remove(event.getLong(ID));

                        disabledPolicyActionTriggers.remove(event.getLong(ID));

                        clear(event.getLong(ID));
                    }

                    case DISABLE_POLICY_ACTION_TRIGGER ->
                    {
                        disabledPolicyActionTriggers.remove(event.getLong(POLICY_ID));

                        var scheduler = SchedulerConfigStore.getStore().getItem(event.getLong(ID));

                        if (scheduler != null)
                        {
                            Bootstrap.configDBService().delete(ConfigDBConstants.COLLECTION_SCHEDULER, new JsonObject().put(ConfigDBConstants.FIELD_NAME, ID).put(VALUE, event.getLong(ID)), DEFAULT_USER, SYSTEM_REMOTE_ADDRESS, future ->
                            {
                                if (future.succeeded())
                                {
                                    SchedulerConfigStore.getStore().deleteItem(event.getLong(ID));

                                    JobScheduler.removeJob(event.getLong(ID));
                                }
                            });
                        }
                    }

                    default ->
                    {
                        // do nothing
                    }
                }
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);
            }

        }).exceptionHandler(LOGGER::error);
    }

    /**
     * Initializes data structures for all policies.
     * This method sets up the necessary arrays and maps for policy evaluation,
     * allocating appropriate space based on policy configuration.
     */
    private void init()
    {
        for (var entry : policies.entrySet())
        {
            var policyContext = entry.getValue().getJsonObject(POLICY_CONTEXT);

            if (policyContext.containsKey(POLICY_RESULT_BY) && !policyContext.getJsonArray(POLICY_RESULT_BY).isEmpty())
            {
                policyGroups.computeIfAbsent(entry.getKey(), value -> new String[MAX_GROUPS]);

                policyGroupIndices.computeIfAbsent(entry.getKey(), value -> new HashMap<>());

                policySumValues.computeIfAbsent(entry.getKey(), value -> new long[MAX_GROUPS]);

                policyCountValues.computeIfAbsent(entry.getKey(), value -> new int[MAX_GROUPS]);
            }
            else
            {
                policySumValues.computeIfAbsent(entry.getKey(), value -> new long[1]);

                policyCountValues.computeIfAbsent(entry.getKey(), value -> new int[1]);
            }

            policyCurrentIndices.put(entry.getKey(), 0);
        }
    }

    /**
     * Initializes data structures for a specific policy.
     * This method sets up the necessary arrays and maps for evaluating a single policy,
     * allocating appropriate space based on the policy's configuration.
     *
     * @param policy   The policy configuration
     * @param policyId The ID of the policy to initialize
     */
    private void init(JsonObject policy, long policyId)
    {
        var policyContext = policy.getJsonObject(POLICY_CONTEXT);

        if (policyContext.containsKey(POLICY_RESULT_BY) && !policyContext.getJsonArray(POLICY_RESULT_BY).isEmpty())
        {
            policyGroups.put(policyId, new String[MAX_GROUPS]);

            policyGroupIndices.put(policyId, new HashMap<>());

            policySumValues.put(policyId, new long[MAX_GROUPS]);

            policyCountValues.put(policyId, new int[MAX_GROUPS]);

            groupingCounters.remove(policyId);
        }
        else
        {
            policySumValues.put(policyId, new long[1]);

            policyCountValues.put(policyId, new int[1]);
        }

        evaluatedPolicies.remove(policyId);

        policyCurrentIndices.put(policyId, 0);

        policyEvaluationTicks.remove(policyId);
    }

    /**
     * Cleans up data structures for a specific policy.
     * This method removes all arrays, maps, and cached data associated with a policy
     * when it is deleted or no longer needed.
     *
     * @param policyId The ID of the policy to clean up
     */
    private void clear(long policyId)
    {
        policyCurrentIndices.remove(policyId);

        policySumValues.remove(policyId);

        policyCountValues.remove(policyId);

        policyGroups.remove(policyId);

        policyGroupIndices.remove(policyId);

        groupingCounters.remove(policyId);

        evaluatedPolicies.remove(policyId);

        policyEvaluationTicks.remove(policyId);
    }

    /**
     * Inspects incoming events against in-memory policies.
     * This method processes events, evaluates them against policy conditions,
     * and triggers actions when conditions are met.
     *
     * @param event The event to inspect, containing event data and metadata
     */
    private void inspect(JsonObject event) //inspect for in-memory polies
    {
        try
        {

            var policy = policies.get(event.getLong(POLICY_ID));

            if (policy != null)
            {
                var context = policy.getJsonObject(POLICY_CONTEXT);

                if (!event.containsKey(EVENT_TIMESTAMP))
                {
                    event.put(EVENT_TIMESTAMP, DateTimeUtil.currentSeconds());
                }

                if (event.containsKey(EVENT) && EVENT_TRAP.equalsIgnoreCase(event.getString(EVENT))) //trap policy
                {
                    event.put(EVENT_TIMESTAMP, CommonUtil.getLong(event.getValue(EVENT_TIMESTAMP)));

                    records.clear();

                    record.clear();

                    if (PolicyEngineConstants.evaluateCondition(true, context.getString(OPERATOR), context.getValue(VALUE), event.getString(context.getString(VisualizationConstants.DATA_POINT))))
                    {
                        if (YES.equalsIgnoreCase(policy.getString(POLICY_CLEAR_STATE, NO)) && PolicyEngineConstants.filterTrap(context.getJsonObject(POLICY_CLEAR_FILTERS).getJsonObject(DATA_FILTER, null), event))
                        {
                            //update severity as CLEAR severity and removing unnecessary fields...
                            event.remove(SNMPTrapProcessor.SNMP_TRAP_VARIABLES);

                            records.add(record.put(context.getString(VisualizationConstants.DATA_POINT) + CARET_SEPARATOR + DatastoreConstants.AggregationType.LAST, event));

                            trigger(policy, context, event, event.getLong(EVENT_TIMESTAMP), Severity.CLEAR.name());
                        }
                        else
                        {
                            //removing unnecessary fields...
                            event.remove(SNMPTrapProcessor.SNMP_TRAP_VARIABLES);

                            records.add(record.put(context.getString(VisualizationConstants.DATA_POINT) + CARET_SEPARATOR + DatastoreConstants.AggregationType.LAST, event));

                            trigger(policy, context, event, event.getLong(EVENT_TIMESTAMP), context.getString(POLICY_SEVERITY));
                        }
                    }
                }
                else if (event.containsKey("inspect"))//after evaluation window complete will be evaluating policy
                {
                    if (CommonUtil.traceEnabled())
                    {
                        LOGGER.trace(String.format("Policy: [%s], going for inspect", policy.getString(POLICY_NAME)));
                    }

                    var sumValues = policySumValues.get(policy.getLong(ID));

                    var countValues = policyCountValues.get(policy.getLong(ID));

                    records.clear();

                    record.clear();

                    if (context.containsKey(POLICY_RESULT_BY) && !context.getJsonArray(POLICY_RESULT_BY).isEmpty())
                    {
                        var groups = policyGroups.get(policy.getLong(ID));

                        var index = policyCurrentIndices.get(policy.getLong(ID)) > TOP_N_GROUPS ? TOP_N_GROUPS : policyCurrentIndices.get(policy.getLong(ID));

                        if (context.getString(VisualizationConstants.AGGREGATOR).equalsIgnoreCase(DatastoreConstants.AggregationType.SUM.getName()))
                        {
                            sort(true, sumValues, countValues, groups, policyGroupIndices.get(policy.getLong(ID)), 0, policyCurrentIndices.get(policy.getLong(ID)) - 1);

                            for (var i = 0; i < index; i++)
                            {
                                evaluate(policy, context, sumValues[i], groups, i);
                            }

                        }
                        else if (context.getString(VisualizationConstants.AGGREGATOR).equalsIgnoreCase(DatastoreConstants.AggregationType.AVG.getName()))
                        {
                            sort(true, sumValues, countValues, groups, policyGroupIndices.get(policy.getLong(ID)), 0, policyCurrentIndices.get(policy.getLong(ID)) - 1);

                            for (var i = 0; i < index; i++)
                            {
                                evaluate(policy, context, sumValues[i] / countValues[i], groups, i);
                            }
                        }
                        else
                        {
                            sort(false, sumValues, countValues, groups, policyGroupIndices.get(policy.getLong(ID)), 0, policyCurrentIndices.get(policy.getLong(ID)) - 1);

                            for (var i = 0; i < index; i++)
                            {
                                evaluate(policy, context, countValues[i], groups, i);
                            }
                        }

                        Arrays.fill(countValues, 0);

                        Arrays.fill(sumValues, 0);

                        if (!records.isEmpty())
                        {
                            if (CommonUtil.debugEnabled())
                            {
                                LOGGER.debug(policy.getString(POLICY_NAME) + " --> " + records.encode());
                            }

                            trigger(policy, context, event, policyEvaluationTicks.get(policy.getLong(ID)), context.getString(POLICY_SEVERITY));
                        }

                        policyCurrentIndices.put(policy.getLong(ID), 0);

                        Arrays.fill(policyGroups.get(policy.getLong(ID)), null);

                        policyGroupIndices.get(policy.getLong(ID)).clear();
                    }

                    else
                    {
                        long value;

                        if (context.getString(VisualizationConstants.AGGREGATOR).equalsIgnoreCase(DatastoreConstants.AggregationType.SUM.getName()) || context.getString(VisualizationConstants.AGGREGATOR).equalsIgnoreCase(DatastoreConstants.AggregationType.AVG.getName()))
                        {
                            value = sumValues[0];

                            if (context.getString(VisualizationConstants.AGGREGATOR).equalsIgnoreCase(DatastoreConstants.AggregationType.AVG.getName()))
                            {
                                value = value / countValues[0];
                            }
                        }
                        else
                        {
                            value = countValues[0];
                        }

                        if (PolicyEngineConstants.evaluateCondition(true, context.getString(OPERATOR), context.getValue(VALUE), value))
                        {
                            records.add(record.put(context.getString(VisualizationConstants.DATA_POINT) + CARET_SEPARATOR + context.getString(VisualizationConstants.AGGREGATOR), value));

                            trigger(policy, context, event, policyEvaluationTicks.get(policy.getLong(ID)), context.getString(POLICY_SEVERITY));
                        }

                        //reinitializing
                        Arrays.fill(sumValues, 0);

                        Arrays.fill(countValues, 0);
                    }

                    evaluatedPolicies.remove(policy.getLong(ID));

                    policyEvaluationTicks.remove(policy.getLong(ID));
                }
                else
                {
                    if ((!policy.containsKey(POLICY_SCHEDULED) || policy.getString(POLICY_SCHEDULED).equalsIgnoreCase(NO)) && !evaluatedPolicies.containsKey(policy.getLong(ID)))//if its not scheduled policy so will be starting its evaluation timer
                    {
                        if (DateTimeUtil.SECOND.equalsIgnoreCase(context.getString(EVALUATION_WINDOW_UNIT)))
                        {
                            evaluatedPolicies.put(policy.getLong(ID), context.getInteger(EVALUATION_WINDOW));
                        }
                        else if (DateTimeUtil.MINUTE.equalsIgnoreCase(context.getString(EVALUATION_WINDOW_UNIT)))
                        {
                            evaluatedPolicies.put(policy.getLong(ID), context.getInteger(EVALUATION_WINDOW) * 60);
                        }
                        else if (DateTimeUtil.HOUR.equalsIgnoreCase(context.getString(EVALUATION_WINDOW_UNIT)))
                        {
                            evaluatedPolicies.put(policy.getLong(ID), context.getInteger(EVALUATION_WINDOW) * 60 * 60);
                        }
                        else if (DateTimeUtil.DAY.equalsIgnoreCase(context.getString(EVALUATION_WINDOW_UNIT)))
                        {
                            evaluatedPolicies.put(policy.getLong(ID), context.getInteger(EVALUATION_WINDOW) * 60 * 60 * 24);
                        }
                    }

                    var index = policyCurrentIndices.get(policy.getLong(ID));

                    var sumValues = policySumValues.get(policy.getLong(ID));

                    var countValues = policyCountValues.get(policy.getLong(ID));

                    if (index >= countValues.length)
                    {
                        LOGGER.warn(String.format("policy: %s, Unique groups are more than %d, increase max group count or configure policy", policy.getString(POLICY_NAME), MAX_GROUPS));

                        inspect(new JsonObject().put("inspect", YES).put(PolicyEngineConstants.POLICY_ID, policy.getLong(ID)));
                    }
                    else
                    {
                        policyEvaluationTicks.computeIfAbsent(policy.getLong(ID), value -> event.getLong(EVENT_TIMESTAMP));

                        if (context.containsKey(POLICY_RESULT_BY) && !context.getJsonArray(POLICY_RESULT_BY).isEmpty())
                        {
                            //if policy has grouping fields so gathering data for unique values
                            var groups = policyGroups.get(policy.getLong(ID));

                            groupingKeys.setLength(0);

                            var groupByFields = context.getJsonArray(POLICY_RESULT_BY);

                            var addGroupingCounters = false;

                            if (!groupingCounters.containsKey(policy.getLong(ID)))//will be adding grouping counternames reason for taking 4 is max group by supported is 4
                            {
                                groupingCounters.computeIfAbsent(policy.getLong(ID), value -> new String[4]);

                                addGroupingCounters = true;
                            }

                            for (var i = 0; i < groupByFields.size(); i++)//generating grouping unique keys
                            {
                                groupingKeys.append(event.getValue(groupByFields.getString(i))).append(COLUMN_SEPARATOR);

                                if (addGroupingCounters)//adding grouping counters its one time activity
                                {
                                    groupingCounters.get(policy.getLong(ID))[i] = groupByFields.getString(i);
                                }
                            }

                            var groupingKey = groupingKeys.deleteCharAt(groupingKeys.length() - 1).toString();

                            var groupingKeyIndex = index;

                            if (!policyGroupIndices.get(policy.getLong(ID)).containsKey(groupingKey))
                            {
                                if (CommonUtil.traceEnabled())
                                {
                                    LOGGER.trace(String.format("Policy: [%s], new grouping key: %s, grouping index: %s", policy.getString(POLICY_NAME), groupingKey, groupingKeyIndex));
                                }

                                policyGroupIndices.get(policy.getLong(ID)).put(groupingKey, groupingKeyIndex);

                                index++;

                                policyCurrentIndices.put(policy.getLong(ID), index);
                            }
                            else
                            {
                                groupingKeyIndex = policyGroupIndices.get(policy.getLong(ID)).get(groupingKey);
                            }

                            countValues[groupingKeyIndex] = countValues[groupingKeyIndex] + 1;

                            groups[groupingKeyIndex] = groupingKey;


                            if (context.getString(VisualizationConstants.AGGREGATOR).equalsIgnoreCase(DatastoreConstants.AggregationType.SUM.getName()) || context.getString(VisualizationConstants.AGGREGATOR).equalsIgnoreCase(DatastoreConstants.AggregationType.AVG.getName()))
                            {
                                sumValues[groupingKeyIndex] = sumValues[groupingKeyIndex] + CommonUtil.getLong(event.getValue(context.getString(VisualizationConstants.DATA_POINT)));//as for sum and average adding previous value with current
                            }
                        }
                        else
                        {
                            //for simple policy will be incrementing count and if aggregator sum or avg adding it to sum context..
                            countValues[index] = countValues[index] + 1;

                            if (context.getString(VisualizationConstants.AGGREGATOR).equalsIgnoreCase(DatastoreConstants.AggregationType.SUM.getName()) || context.getString(VisualizationConstants.AGGREGATOR).equalsIgnoreCase(DatastoreConstants.AggregationType.AVG.getName()))
                            {
                                sumValues[index] = sumValues[index] + CommonUtil.getLong(event.getValue(context.getString(VisualizationConstants.DATA_POINT)));
                            }
                        }
                    }
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    /**
     * Inspects events against scheduled policies.
     * This method processes events from scheduled policies, evaluates them against
     * policy conditions, and triggers actions when conditions are met.
     *
     * @param event    The event to inspect, containing event data and metadata
     * @param policyId The ID of the policy to evaluate against
     */
    private void inspect(JsonObject event, long policyId) //inspect for scheduled polies (db events)
    {
        try
        {
            var policy = policies.get(policyId);

            var context = policy.getJsonObject(POLICY_CONTEXT);

            if (!event.containsKey(EVENT_TIMESTAMP))
            {
                event.put(EVENT_TIMESTAMP, DateTimeUtil.currentSeconds());
            }

            if (event.containsKey(RESULT)) //visualization response of scheduled policy
            {
                records.clear();

                var rows = VisualizationConstants.unpack(Buffer.buffer(event.getBinary(RESULT)), LOGGER, false, null, true, true).getJsonArray(RESULT);

                if (rows != null && !rows.isEmpty())
                {
                    var fieldKey = context.getString(VisualizationConstants.DATA_POINT) + CARET_SEPARATOR + context.getString(VisualizationConstants.AGGREGATOR);

                    for (var index = 0; index < rows.size(); index++)
                    {
                        var row = rows.getJsonObject(index);

                        if (row.containsKey(fieldKey) && PolicyEngineConstants.evaluateCondition(true, context.getString(OPERATOR), context.getValue(VALUE), row.getValue(fieldKey)))
                        {
                            records.add(row);
                        }
                    }

                    if (!records.isEmpty())
                    {
                        trigger(policy, context, event, DUMMY_ID, context.getString(POLICY_SEVERITY));
                    }
                }
            }
            else if (event.containsKey("schedule")) // prepare context for scheduled policy
            {
                prepareContext(policy.getLong(ID), policy);
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    /**
     * Evaluates a value against policy conditions.
     * This method checks if a value meets the policy conditions and adds
     * a record to the results if the conditions are met.
     *
     * @param policy     The policy configuration
     * @param context    The policy context containing conditions
     * @param value      The value to evaluate
     * @param topNGroups Array of top N group values
     * @param index      The index of the current group in the topNGroups array
     */
    private void evaluate(JsonObject policy, JsonObject context, Object value, String[] topNGroups, int index)
    {
        try
        {
            if (PolicyEngineConstants.evaluateCondition(true, context.getString(OPERATOR), context.getValue(VALUE), value))
            {
                var grouping = CommonUtil.getString(topNGroups[index]);

                var record = new JsonObject();

                record.put(context.getString(VisualizationConstants.DATA_POINT) + CARET_SEPARATOR + context.getString(VisualizationConstants.AGGREGATOR), value);

                if (grouping.contains(COLUMN_SEPARATOR))
                {
                    var values = grouping.split(COLUMN_SEPARATOR);

                    for (var j = 0; j < values.length; j++)
                    {
                        record.put(groupingCounters.get(policy.getLong(ID))[j], values[j]);
                    }
                }
                else
                {

                    record.put(groupingCounters.get(policy.getLong(ID))[0], grouping);
                }

                records.add(record);
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    /**
     * Prepares the context for policy evaluation.
     * This method creates a visualization context based on the policy configuration,
     * setting up data sources, filters, and query parameters for policy evaluation.
     *
     * @param policyId The ID of the policy to prepare context for
     * @param policy   The policy configuration
     */
    private void prepareContext(long policyId, JsonObject policy)
    {
        JsonObject context;

        var policyContext = policy.getJsonObject(POLICY_CONTEXT);

        if (policyContext.containsKey(POLICY_RESULT_BY) && !policyContext.getJsonArray(POLICY_RESULT_BY).isEmpty())
        {
            context = new JsonObject(VisualizationConstants.VISUALIZATION_TOP_N_CONTEXT.replace("@@@", policyContext.getString(VisualizationConstants.DATA_POINT) + "." + policyContext.getString(VisualizationConstants.AGGREGATOR)));
        }
        else
        {
            context = new JsonObject(VisualizationConstants.VISUALIZATION_GAUGE_CONTEXT);
        }

        var dataSource = context.getJsonArray(VisualizationConstants.VISUALIZATION_DATA_SOURCES).getJsonObject(0);

        if (policy.getString(POLICY_TYPE).equalsIgnoreCase(PolicyType.LOG.getName()))
        {
            dataSource.put(VisualizationConstants.TYPE, VisualizationConstants.VisualizationDataSource.LOG.getName());

            dataSource.put(VisualizationConstants.CATEGORY, VisualizationConstants.VisualizationDataSource.LOG.getName());
        }
        else
        {
            dataSource.put(VisualizationConstants.TYPE, VisualizationConstants.VisualizationDataSource.FLOW.getName());

            dataSource.put(VisualizationConstants.CATEGORY, VisualizationConstants.VisualizationDataSource.FLOW.getName());
        }

        if (policyContext.containsKey(GlobalConstants.FILTERS))
        {
            dataSource.put(GlobalConstants.FILTERS, policyContext.getJsonObject(GlobalConstants.FILTERS));
        }

        if (policyContext.containsKey(POLICY_RESULT_BY))
        {
            dataSource.put(VisualizationConstants.VISUALIZATION_RESULT_BY, policyContext.getJsonArray(POLICY_RESULT_BY));
        }

        var dataPoint = new JsonObject().put(VisualizationConstants.AGGREGATOR, policyContext.getString(VisualizationConstants.AGGREGATOR)).put(VisualizationConstants.DATA_POINT, policyContext.getString(VisualizationConstants.DATA_POINT));

        if (!policyContext.getJsonArray(ENTITIES).isEmpty())
        {
            dataPoint.put(ENTITIES, policyContext.getJsonArray(ENTITIES));
        }

        if (policyContext.containsKey(ENTITY_TYPE) && !policyContext.getString(ENTITY_TYPE).isEmpty())
        {
            dataPoint.put(ENTITY_TYPE, policyContext.getString(ENTITY_TYPE));
        }

        if (policyContext.getValue(EVALUATION_WINDOW) != null)
        {
            String timeline;

            if (policy.getString(Scheduler.SCHEDULER_TIMELINE).equalsIgnoreCase("Daily"))
            {
                timeline = "-1d";
            }
            else if (policy.getString(Scheduler.SCHEDULER_TIMELINE).equalsIgnoreCase("Weekly"))
            {
                timeline = VisualizationConstants.VisualizationTimeline.LAST_7_DAYS.getName();
            }
            else if (policy.getString(Scheduler.SCHEDULER_TIMELINE).equalsIgnoreCase("Monthly"))
            {
                timeline = VisualizationConstants.VisualizationTimeline.LAST_30_DAYS.getName();
            }
            else
            {
                timeline = VisualizationConstants.VisualizationTimeline.LAST_1_HOUR.getName();
            }

            context.getJsonObject(VisualizationConstants.VISUALIZATION_TIMELINE).put(VisualizationConstants.RELATIVE_TIMELINE, timeline);
        }

        dataSource.put(VisualizationConstants.DATA_POINTS, new JsonArray().add(dataPoint));

        vertx.eventBus().send(EVENT_VISUALIZATION, context.put(PolicyEngineConstants.POLICY_ID, policyId).put(User.USER_NAME, DEFAULT_USER).put(EVENT_TYPE, EVENT_EVENT_POLICY));
    }

    /**
     * Triggers policy actions when conditions are met.
     * This method handles the core logic of policy triggering, including:
     * - Recording policy trigger events
     * - Managing policy suppression
     * - Delegating to specific action handlers
     *
     * @param policy   The policy configuration
     * @param context  The policy context containing condition information
     * @param event    The event that triggered the policy
     * @param tick     The timestamp when the policy was triggered
     * @param severity The severity level of the triggered policy
     */
    private void trigger(JsonObject policy, JsonObject context, JsonObject event, long tick, String severity)
    {
        try
        {
            if (YES.equalsIgnoreCase(policy.getString(POLICY_STATE)))
            {
                EventPolicyCacheStore.getStore().updateTriggerTicks(policy.getLong(ID), event.getLong(EVENT_TIMESTAMP));

                if (!event.containsKey(VisualizationConstants.VISUALIZATION_TIMELINE))
                {
                    event.put(VisualizationConstants.VISUALIZATION_TIMELINE, new JsonObject().put(VisualizationConstants.FROM_DATETIME, tick * 1000).put(VisualizationConstants.TO_DATETIME, (evaluatedPolicies.get(policy.getLong(ID)) == null ? DateTimeUtil.currentSeconds() : tick + evaluatedPolicies.get(policy.getLong(ID))) * 1000));
                }

                var id = CommonUtil.newEventId();

                var message = context.getString(VisualizationConstants.DATA_POINT) + " " + context.getString(VisualizationConstants.AGGREGATOR, EMPTY_VALUE) + " " + PolicyEngineConstants.toOperatorLiteral(context.getString(OPERATOR)) + " " + context.getValue(VALUE);

                if (Operator.RANGE.getName().equalsIgnoreCase(context.getString(OPERATOR)))
                {
                    message = context.getString(VisualizationConstants.DATA_POINT) + " " + context.getString(VisualizationConstants.AGGREGATOR, EMPTY_VALUE) + " " + PolicyEngineConstants.toOperatorLiteral(context.getString(OPERATOR)) + " " + context.getJsonArray(VALUE).getString(0) + " and " + context.getJsonArray(VALUE).getString(1);
                }

                var notificationContext = new JsonObject().put(EventPolicy.TRIGGER_MODE, context.getString(EventPolicy.TRIGGER_MODE)).put(EventBusConstants.EVENT_TIMESTAMP, event.getLong(EVENT_TIMESTAMP)).put(SEVERITY, severity.toLowerCase()).put(EVENT_FIELD, context.getString(VisualizationConstants.DATA_POINT)).put(VALUE, records.size()).put(EVALUATION_WINDOW, DateTimeUtil.getDateTime(event.getJsonObject(VisualizationConstants.VISUALIZATION_TIMELINE))).put(TRIGGERED_VALUE, context.getValue(VALUE));

                DatastoreConstants.write(new JsonObject()
                        .put(SEVERITY, severity)
                        .put(ID, id)
                        .put(MESSAGE, message).put(NETROUTE_ID, 0)
                        .put(POLICY_TYPE, policy.getString(POLICY_TYPE))//TODO Need to change type as per PMG
                        .put(POLICY_ID, policy.getLong(ID)).put(EVENT_FIELD, context.getString(VisualizationConstants.DATA_POINT))
                        .put(GlobalConstants.PLUGIN_ID, policy.getString(POLICY_TYPE).equalsIgnoreCase(PolicyType.FLOW.getName()) ? DatastoreConstants.PluginId.POLICY_FLOW.getName() : policy.getString(POLICY_TYPE).equalsIgnoreCase(PolicyType.TRAP.getName()) ? DatastoreConstants.PluginId.POLICY_TRAP.getName() : DatastoreConstants.PluginId.POLICY_EVENT.getName())
                        .put(EventBusConstants.EVENT_TIMESTAMP, event.getLong(EVENT_TIMESTAMP, DateTimeUtil.currentSeconds()))
                        .put(DatastoreConstants.DATASTORE_TYPE, DatastoreConstants.DatastoreType.EVENT_POLICY.ordinal()).put(EVENT_SOURCE, PolicyType.TRAP.getName().equalsIgnoreCase(policy.getString(POLICY_TYPE)) ? event.getString(EVENT_SOURCE) : LOCAL_HOST), VisualizationConstants.VisualizationDataSource.POLICY.getName(), mappers, builder);

                DatastoreConstants.write(new JsonObject()
                        .put(POLICY_TRIGGER_ID, id).put(SEVERITY, severity).put(POLICY_TRIGGER_EVALUATION_WINDOW, event.getJsonObject(VisualizationConstants.VISUALIZATION_TIMELINE).encode())
                        .put(POLICY_TRIGGER_VALUE, new JsonObject().put(RESULT, records).encode())
                        .put(POLICY_TRIGGER_POLICY_ID, policy.getLong(ID)).put(GlobalConstants.PLUGIN_ID, DatastoreConstants.PluginId.POLICY_RESULT.getName())
                        .put(EVENT_TIMESTAMP, event.getLong(EVENT_TIMESTAMP, DateTimeUtil.currentSeconds()))
                        .put(DatastoreConstants.DATASTORE_TYPE, DatastoreConstants.DatastoreType.POLICY_RESULT.ordinal()).put(EVENT_SOURCE, PolicyType.TRAP.getName().equalsIgnoreCase(policy.getString(POLICY_TYPE)) ? event.getString(EVENT_SOURCE) : LOCAL_HOST), VisualizationConstants.VisualizationDataSource.POLICY_RESULT.getName(), mappers, builder);

                Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_POLICY_NOTIFICATION,
                        new JsonObject().put(POLICY_NAME, policy.getString(POLICY_NAME))
                                .put(POLICY_TYPE, policy.getString(POLICY_TYPE))
                                .put(POLICY_ID, policy.getString(ID))
                                .put(SEVERITY, severity)
                                .put(EVENT_TIMESTAMP, event.getLong(EVENT_TIMESTAMP))
                                .put(EVENT_SOURCE, PolicyType.TRAP.getName().equalsIgnoreCase(policy.getString(POLICY_TYPE)) ? event.getString(EVENT_SOURCE) : EMPTY_VALUE)
                                .mergeIn(EventPolicyCacheStore.getStore().getTriggerTicks(policy.getLong(ID))));

                if (policy.getString(POLICY_SUPPRESS_ACTION, NO).equalsIgnoreCase(YES) && !disabledPolicyActionTriggers.contains(policy.getLong(ID)))
                {
                    disabledPolicyActionTriggers.add(policy.getLong(ID));

                    //on policy suppression once notification is sent after that it is suppressed for particular selected time
                    triggerAction(severity, policy, event, message, notificationContext);

                    var time = policy.getLong(POLICY_SUPPRESS_WINDOW);

                    if (policy.getString(POLICY_SUPPRESS_WINDOW_UNIT).equalsIgnoreCase(DateTimeUtil.MINUTE))
                    {
                        time *= 60;
                    }
                    else if (policy.getString(POLICY_SUPPRESS_WINDOW_UNIT).equalsIgnoreCase(DateTimeUtil.HOUR))
                    {
                        time *= 60 * 60;
                    }
                    else if (policy.getString(POLICY_SUPPRESS_WINDOW_UNIT).equalsIgnoreCase(DateTimeUtil.DAY))
                    {
                        time *= 60 * 60 * 24;
                    }

                    var dateTime = DateTimeUtil.getScheduledTimestamp(System.currentTimeMillis() + (time * 1000L));

                    Bootstrap.configDBService().save(ConfigDBConstants.COLLECTION_SCHEDULER, new JsonObject().put(POLICY_ID, policy.getLong(ID)).put(Scheduler.SCHEDULER_TIMES, new JsonArray().add(dateTime.split(" ")[1])).put(Scheduler.SCHEDULER_START_DATE, dateTime.split(" ")[0]).put(Scheduler.SCHEDULER_JOB_TYPE, JobScheduler.JobType.POLICY_ACTION_TRIGGER_DISABLE.getName()).put(Scheduler.SCHEDULER_STATE, YES).put(Scheduler.SCHEDULER_TIMELINE, CronExpressionUtil.CRON_ONCE), DEFAULT_USER, SYSTEM_REMOTE_ADDRESS, future ->
                    {
                        if (future.succeeded())
                        {
                            SchedulerConfigStore.getStore().addItem(future.result()).onComplete(result -> JobScheduler.scheduleCustomJob(SchedulerConfigStore.getStore().getItem(future.result())));

                            vertx.eventBus().send(EventBusConstants.EVENT_USER_NOTIFICATION, new JsonObject().put(STATUS, STATUS_SUCCEED).put(EVENT_TYPE, EVENT_POLICY_ACTION).put(MESSAGE, String.format(InfoMessageConstants.POLICY_SUPPRESSED, policy.getString(POLICY_NAME), DEFAULT_USER, dateTime)));
                        }
                    });
                }

                if (!disabledPolicyActionTriggers.contains(policy.getLong(ID)))
                {
                    triggerAction(severity, policy, event.put(EVENT_TIMESTAMP, event.getLong(EVENT_TIMESTAMP)), message, notificationContext);
                }
            }
            else
            {
                if (CommonUtil.traceEnabled())
                {
                    LOGGER.trace(String.format("Policy: [%s], Not triggered. Reason: Policy Disabled", policy.getString(POLICY_NAME)));
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    /**
     * Executes specific actions defined in the policy configuration.
     * This method handles different types of actions including:
     * - Email notifications
     * - SMS notifications
     * - Sound notifications
     * - Channel notifications (Teams, Slack, etc.)
     * - Runbook executions
     *
     * @param severity The severity level of the triggered policy
     * @param policy   The policy configuration
     * @param event    The event that triggered the policy
     * @param message  The notification message
     * @param context  The context containing values for message placeholders
     */
    private void triggerAction(String severity, JsonObject policy, JsonObject event, String message, JsonObject context)
    {
        var actions = policy.getJsonObject(POLICY_ACTIONS);

        if (actions != null && actions.containsKey(PolicyTriggerActionType.NOTIFICATION.getName())
                && actions.getJsonObject(PolicyTriggerActionType.NOTIFICATION.getName()) != null
                && !actions.getJsonObject(PolicyTriggerActionType.NOTIFICATION.getName()).isEmpty())
        {
            var notificationContext = actions.getJsonObject(PolicyTriggerActionType.NOTIFICATION.getName());

            context.put(POLICY_URL, String.format(POLICY_URL_VALUE, Base64.getEncoder().encodeToString(new JsonObject().put(POLICY_DRILL_DOWN_TEMPLATE, YES).put(PolicyEngineConstants.POLICY_ID, policy.getLong(ID, 0L)).put(EVENT_SOURCE, PolicyType.TRAP.getName().equalsIgnoreCase(policy.getString(POLICY_TYPE)) ? event.getString(EVENT_SOURCE) : LOCAL_HOST).put(METRIC, policy.getString(METRIC, EMPTY_VALUE)).put(POLICY_TYPE, policy.getString(POLICY_TYPE, EMPTY_VALUE)).encode().getBytes())));

            if (notificationContext.containsKey(Notification.NotificationType.EMAIL.getName())
                    && !notificationContext.getJsonObject(Notification.NotificationType.EMAIL.getName()).isEmpty())
            {
                var emailRecipients = new HashSet<String>();

                var smsRecipients = new HashSet<String>();

                PolicyEngineConstants.setRecipients(emailRecipients, smsRecipients, notificationContext.getJsonObject(Notification.NotificationType.EMAIL.getName()).getJsonArray(severity));

                if (!emailRecipients.isEmpty())
                {
                    var recipients = new JsonArray(new ArrayList<>(emailRecipients));

                    var subject = EVENT_TRAP.equalsIgnoreCase(event.getString(EVENT)) ? PolicyEngineConstants.replaceTrapPolicyPlaceholders(policy, context, policy.getString(POLICY_TITLE) != null && !policy.getString(POLICY_TITLE).trim().isEmpty() ? policy.getString(POLICY_TITLE) : InfoMessageConstants.POLICY_EVENT_DEFAULT_EMAIL_SUBJECT.replace("$$$severity$$$", severity), message, event) : PolicyEngineConstants.replaceEventPolicyPlaceholders(policy, context, policy.getString(POLICY_TITLE) != null && !policy.getString(POLICY_TITLE).trim().isEmpty() ? policy.getString(POLICY_TITLE) : InfoMessageConstants.POLICY_EVENT_DEFAULT_EMAIL_SUBJECT.replace("$$$severity$$$", severity), message);

                    // MOTADATA-3974
                    event.put(RunbookPlugin.RUNBOOK_PLUGIN_NOTIFICATION_EMAIL_RECIPIENTS, recipients);

                    event.put(RunbookPlugin.RUNBOOK_PLUGIN_NOTIFICATION_EMAIL_SUBJECT, subject);

                    event.put(SEVERITY, severity.toLowerCase());

                    context.mergeIn(policy);

                    if (policy.containsKey(POLICY_MESSAGE) && !policy.getString(POLICY_MESSAGE).isEmpty())
                    {
                        context.put(POLICY_MESSAGE, EVENT_TRAP.equalsIgnoreCase(event.getString(EVENT)) ? PolicyEngineConstants.replaceTrapPolicyPlaceholders(policy, context, policy.getString(POLICY_MESSAGE), message, event) : PolicyEngineConstants.replaceEventPolicyPlaceholders(policy, context, policy.getString(POLICY_MESSAGE), message));
                    }
                    else
                    {
                        context.put(POLICY_MESSAGE, message);
                    }

                    vertx.eventBus().<JsonObject>request(EventBusConstants.EVENT_NOTIFICATION, new JsonObject()
                            .put(EventBusConstants.EVENT_REPLY, YES).put(NOTIFICATION_TYPE, Notification.NotificationType.EMAIL.getName())
                            .put(Notification.EMAIL_NOTIFICATION_ATTACHMENT_DISPOSITION_TYPE, "inline").put(Notification.EMAIL_NOTIFICATION_ATTACHMENT_TYPE, "image/png")
                            .put(Notification.EMAIL_NOTIFICATION_ATTACHMENTS, new JsonArray().add(severity.toLowerCase() + ".png").addAll(Notification.EMAIL_NOTIFICATION_INLINE_ATTACHMENT_ICONS))
                            .put(Notification.EMAIL_NOTIFICATION_SUBJECT, subject)
                            .put(Notification.EMAIL_NOTIFICATION_RECIPIENTS, recipients)
                            .put(Notification.EMAIL_NOTIFICATION_CONTENT, new StringSubstitutor(context.put(EVENT_SOURCE, event.getString(EVENT_SOURCE, EMPTY_VALUE)).put(MESSAGE, message).put(TIME_STAMP, DateTimeUtil.timestamp(context.getLong(EVENT_TIMESTAMP) * 1000L)).getMap()).replace(PolicyType.TRAP.getName().equalsIgnoreCase(policy.getString(POLICY_TYPE)) ? Notification.EMAIL_NOTIFICATION_TRAP_POLICY_HTML_TEMPLATE : Notification.EMAIL_NOTIFICATION_EVENT_POLICY_HTML_TEMPLATE).getBytes(StandardCharsets.UTF_8)), DELIVERY_OPTIONS, reply ->
                    {
                        try
                        {
                            if (reply.succeeded())
                            {
                                writeTriggeredActionEvent(event.put(RESULT, new JsonArray().addAll(records)), policy.getLong(ID), reply.result().body().put(RUNBOOK_WORKLOG_TYPE, PolicyTriggerActionType.NOTIFICATION.getName()));
                            }
                        }
                        catch (Exception exception)
                        {
                            LOGGER.error(exception);
                        }
                    });

                    if (MotadataConfigUtil.devMode())
                    {
                        Notification.sendEmail(new JsonObject()
                                .put(EventBusConstants.EVENT_REPLY, YES).put(NOTIFICATION_TYPE, Notification.NotificationType.EMAIL.getName())
                                .put(Notification.EMAIL_NOTIFICATION_ATTACHMENT_DISPOSITION_TYPE, "inline").put(Notification.EMAIL_NOTIFICATION_ATTACHMENT_TYPE, "image/png")
                                .put(Notification.EMAIL_NOTIFICATION_ATTACHMENTS, new JsonArray().add(severity.toLowerCase() + ".png").addAll(Notification.EMAIL_NOTIFICATION_INLINE_ATTACHMENT_ICONS))
                                .put(Notification.EMAIL_NOTIFICATION_SUBJECT, subject)
                                .put(Notification.EMAIL_NOTIFICATION_RECIPIENTS, recipients)
                                .put(Notification.EMAIL_NOTIFICATION_CONTENT, new StringSubstitutor(context.put(TIME_STAMP, DateTimeUtil.timestamp(context.getLong(EVENT_TIMESTAMP) * 1000L)).getMap()).replace(PolicyType.TRAP.getName().equalsIgnoreCase(policy.getString(POLICY_TYPE)) ? Notification.EMAIL_NOTIFICATION_TRAP_POLICY_HTML_TEMPLATE : Notification.EMAIL_NOTIFICATION_EVENT_POLICY_HTML_TEMPLATE).getBytes(StandardCharsets.UTF_8)));
                    }

                }

                if (!smsRecipients.isEmpty())
                {

                    vertx.eventBus().<JsonObject>request(EventBusConstants.EVENT_NOTIFICATION, new JsonObject().put(EventBusConstants.EVENT_REPLY, YES).put(NOTIFICATION_TYPE, Notification.NotificationType.SMS.getName()).put(Notification.SMS_NOTIFICATION_RECIPIENTS, new JsonArray(new ArrayList<>(smsRecipients)))
                            .put(Notification.SMS_NOTIFICATION_MESSAGE, EVENT_TRAP.equalsIgnoreCase(event.getString(EVENT)) ? replaceTrapPolicyPlaceholders(policy, context, policy.getString(POLICY_MESSAGE) != null && !policy.getString(POLICY_MESSAGE).trim().isEmpty() ? policy.getString(POLICY_MESSAGE) : InfoMessageConstants.POLICY_EVENT_DEFAULT_SMS, message, event) : replaceEventPolicyPlaceholders(policy, event, policy.getString(POLICY_MESSAGE) != null && !policy.getString(POLICY_MESSAGE).trim().isEmpty() ? policy.getString(POLICY_MESSAGE) : InfoMessageConstants.POLICY_EVENT_DEFAULT_SMS, message)), DELIVERY_OPTIONS, reply ->
                    {
                        try
                        {
                            if (reply.succeeded())
                            {
                                writeTriggeredActionEvent(event.put(RESULT, new JsonArray().addAll(records)), policy.getLong(ID), reply.result().body().put(RUNBOOK_WORKLOG_TYPE, PolicyTriggerActionType.NOTIFICATION.getName()));
                            }
                        }
                        catch (Exception exception)
                        {
                            LOGGER.error(exception);
                        }
                    });
                }

            }

            if (notificationContext.containsKey(Notification.NotificationType.SOUND.getName())
                    && !notificationContext.getJsonObject(Notification.NotificationType.SOUND.getName()).isEmpty()
                    && notificationContext.getJsonObject(Notification.NotificationType.SOUND.getName()).containsKey(severity))
            {
                Notification.playSound(new JsonObject().mergeIn(context));
            }

            if (notificationContext.containsKey(CHANNELS)
                    && notificationContext.getJsonObject(CHANNELS) != null
                    && !notificationContext.getJsonObject(CHANNELS).isEmpty()
                    && notificationContext.getJsonObject(CHANNELS).containsKey(severity)
                    && notificationContext.getJsonObject(CHANNELS).getJsonArray(severity) != null
                    && !notificationContext.getJsonObject(CHANNELS).getJsonArray(severity).isEmpty())
            {

                if (policy.containsKey(POLICY_MESSAGE) && !policy.getString(POLICY_MESSAGE).isEmpty())
                {
                    context.put(POLICY_MESSAGE, EVENT_TRAP.equalsIgnoreCase(event.getString(EVENT)) ? PolicyEngineConstants.replaceTrapPolicyPlaceholders(policy, context, policy.getString(POLICY_MESSAGE), message, event) : PolicyEngineConstants.replaceEventPolicyPlaceholders(policy, context, policy.getString(POLICY_MESSAGE), message));
                }
                else
                {
                    context.put(POLICY_MESSAGE, message);
                }

                var recipients = notificationContext.getJsonObject(CHANNELS).getJsonArray(severity);

                var channelRecipients = new HashMap<String, Set<String>>();

                for (var index = 0; index < recipients.size(); index++)
                {
                    var recipient = recipients.getJsonObject(index);

                    var item = IntegrationConfigStore.getStore().getItem(recipient.getLong(IntegrationProfile.INTEGRATION));

                    if (item != null)
                    {
                        // Groups the handle list based on the integration type and forwards it to the corresponding notification type.
                        channelRecipients.computeIfAbsent(item.getString(Integration.INTEGRATION_TYPE), key -> new HashSet<>())
                                .add(recipient.getString(ID));
                    }
                }

                channelRecipients.forEach((channelType, targets) ->
                {
                    if (!targets.isEmpty())
                    {
                        var triggerCondition = EMPTY_VALUE;

                        if (!policy.getString(POLICY_TYPE).equalsIgnoreCase(PolicyEngineConstants.PolicyType.AVAILABILITY.getName()) && !severity.equalsIgnoreCase(Severity.CLEAR.name()))
                        {
                            triggerCondition = String.format("metric value (%s)", PolicyEngineConstants.toOperatorLiteral(policy.getJsonObject(POLICY_CONTEXT).getString(OPERATOR)).toLowerCase());
                        }

                        Notification.send(new JsonObject()
                                .mergeIn(context)
                                .put(NOTIFICATION_TYPE, channelType)
                                .put(CHANNELS, new JsonArray(new ArrayList<>(targets)))
                                .put(Notification.CHANNEL_NOTIFICATION_CONTENT, new StringSubstitutor(policy.mergeIn(context)
                                        .put(TIME_STAMP, DateTimeUtil.timestamp(context.getLong(EVENT_TIMESTAMP) * 1000L))
                                        .put(EVENT_SOURCE, event.getString(EVENT_SOURCE, EMPTY_VALUE))
                                        .put("trigger.condition", triggerCondition).getMap())
                                        .replace(Notification.getChannelNotificationTemplate(policy.getString(POLICY_TYPE), channelType, false))));
                    }
                });
            }
        }


        if (actions != null && actions.getJsonObject(PolicyTriggerActionType.RUNBOOK.getName()) != null && !actions.getJsonObject(PolicyTriggerActionType.RUNBOOK.getName()).isEmpty()
                && actions.getJsonObject(PolicyTriggerActionType.RUNBOOK.getName()).containsKey(severity) && !actions.getJsonObject(PolicyTriggerActionType.RUNBOOK.getName()).getJsonArray(severity).isEmpty())
        {
            var entries = actions.getJsonObject(PolicyTriggerActionType.RUNBOOK.getName()).getJsonArray(severity);

            for (var index = 0; index < entries.size(); index++)
            {
                vertx.eventBus().send(EventBusConstants.EVENT_RUNBOOK, getContext(entries.getJsonObject(index).getLong(ID), event, policy));
            }
        }
    }

    /**
     * Creates a context for runbook execution.
     * This method prepares the context for a runbook triggered by a policy,
     * including necessary parameters and metadata.
     *
     * @param id      The ID of the runbook to execute
     * @param context The event context that triggered the policy
     * @param policy  The policy configuration
     * @return A JsonObject containing the runbook execution context
     */
    private JsonObject getContext(long id, JsonObject context, JsonObject policy)
    {
        var runbookContext = new JsonObject().mergeIn(context).put(ID, CommonUtil.getLong(id))
//                .put(RunbookPlugin.RUNBOOK_PLUGIN_ENTITIES, new JsonArray().add(SYSTEM_REMOTE_ADDRESS))
                .put(EventBusConstants.EVENT_REPLY, YES)
                .put(POLICY_ID, policy.getLong(ID))
                .put(POLICY_NAME, policy.getString(POLICY_NAME))
                .put(User.USER_NAME, DEFAULT_USER);

        // MOTADATA-4481
        if (context.containsKey(RunbookPlugin.RUNBOOK_PLUGIN_NOTIFICATION_EMAIL_RECIPIENTS))
        {
            context.remove(RunbookPlugin.RUNBOOK_PLUGIN_NOTIFICATION_EMAIL_RECIPIENTS);

            context.remove(RunbookPlugin.RUNBOOK_PLUGIN_NOTIFICATION_EMAIL_SUBJECT);
        }

        return runbookContext;
    }

    /**
     * Records policy action execution events.
     * This method writes information about triggered policy actions (notifications, runbooks)
     * to the datastore for auditing and reporting purposes.
     *
     * @param event    The event that triggered the policy
     * @param policyId The ID of the policy that was triggered
     * @param context  The context containing action execution results
     */
    private void writeTriggeredActionEvent(JsonObject event, long policyId, JsonObject context)
    {
        var value = EMPTY_VALUE;

        try
        {

            if (PolicyEngineConstants.PolicyTriggerActionType.valueOfName(context.getString(RUNBOOK_WORKLOG_TYPE)) == PolicyEngineConstants.PolicyTriggerActionType.RUNBOOK)
            {
                var result = context.containsKey(EventBusConstants.EVENT_REPLY_CONTEXTS) && !context.getJsonArray(EventBusConstants.EVENT_REPLY_CONTEXTS).isEmpty() ? context.getJsonArray(EventBusConstants.EVENT_REPLY_CONTEXTS).getJsonObject(0) : null;

                if (result != null && !result.isEmpty())
                {
                    if (result.containsKey(RESULT))
                    {
                        value = CommonUtil.getString(result.getValue(RESULT));
                    }

                    context.put(STATUS, result.getValue(STATUS));
                }

                context.put(RUNBOOK_WORKLOG_TYPE, PolicyEngineConstants.PolicyTriggerActionType.RUNBOOK.ordinal());
            }
            else
            {
                value = CommonUtil.getString(context.getJsonArray("recipients"));

                context.put(RUNBOOK_WORKLOG_TYPE, PolicyTriggerActionType.NOTIFICATION.ordinal());
            }

            var error = context.getString(ERROR);

            DatastoreConstants.write(new JsonObject().put(RUNBOOK_WORKLOG_STATUS, context.getValue(STATUS, STATUS_FAIL))
                            .put(RUNBOOK_WORKLOG_RESULT, value.length() > RUNBOOK_WORKLOG_RESULT_MAX_BYTES ? value.substring(0, RUNBOOK_WORKLOG_RESULT_MAX_BYTES) : value)
                            .put(RUNBOOK_WORKLOG_ID, context.containsKey(ID) ? context.getLong(ID) : 0)
                            .put(RUNBOOK_WORKLOG_TYPE, context.getValue(RUNBOOK_WORKLOG_TYPE))
                            .put(POLICY_ID, policyId)
                            .put(AIOpsObject.OBJECT_ID, 0).put(NetRoute.NETROUTE_ID, 0)
                            .put(GlobalConstants.PLUGIN_ID, DatastoreConstants.PluginId.RUNBOOK_WORKLOG.getName())
                            .put(EVENT_TIMESTAMP, event.getLong(EVENT_TIMESTAMP))
                            .put(DatastoreConstants.DATASTORE_TYPE, DatastoreConstants.DatastoreType.RUNBOOK_WORKLOG.ordinal())
                            .put(USER_NAME, DEFAULT_USER)
                            .put(RUNBOOK_WORKLOG_ERROR, error != null ? error.length() > RUNBOOK_WORKLOG_RESULT_MAX_BYTES ? error.substring(0, RUNBOOK_WORKLOG_RESULT_MAX_BYTES) : error : EMPTY_VALUE)
                            .put(EVENT_SOURCE, EVENT_TRAP.equalsIgnoreCase(event.getString(EVENT)) ? event.getString(EVENT_SOURCE) : LOCAL_HOST),
                    VisualizationConstants.VisualizationDataSource.RUNBOOK_WORKLOG.getName(), mappers, builder);

        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    /**
     * Enriches policy configuration with additional processing.
     * This method handles special cases like range operators, converting
     * string representations to structured data formats.
     *
     * @param policy The policy configuration to enrich
     * @return The enriched policy configuration
     */
    private JsonObject enrich(JsonObject policy)
    {
        try
        {
            var context = policy.getJsonObject(POLICY_CONTEXT);

            if (Operator.RANGE.getName().equalsIgnoreCase(context.getString(OPERATOR)))
            {
                var values = context.getString(VALUE).split(HASH_SEPARATOR);

                context.put(VALUE, new JsonArray().add(values[0]).add(values[1]));
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return policy;
    }

    /**
     * Stops the EventPolicyInspector verticle.
     * This method cleans up resources and stops the event engine
     * when the verticle is undeployed.
     *
     * @param promise Promise to be completed when shutdown is done
     * @throws Exception If an error occurs during shutdown
     */
    @Override
    public void stop(Promise<Void> promise) throws Exception
    {
        eventEngine.stop(vertx, promise);
    }
}
