/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 *	Change Logs:
 *	Date			Author			    Notes
 *  28-Feb-2025		Darshan Parmar		MOTADATA-5215: SonarQube Suggestions Resolution
 *  19-May-2025     Sankalp             MOTADATA-5939: Cisco ACI Topology support
 */

package com.mindarray.aiops;

import com.mindarray.ErrorMessageConstants;
import com.mindarray.GlobalConstants;
import com.mindarray.api.AIOpsObject;
import com.mindarray.api.DependencyMapper;
import com.mindarray.api.Metric;
import com.mindarray.api.SystemProcess;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.ha.HAConstants;
import com.mindarray.nms.NMSConstants;
import com.mindarray.store.ObjectConfigStore;
import com.mindarray.util.CodecUtil;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import io.vertx.core.AbstractVerticle;
import io.vertx.core.Promise;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;

import java.util.*;
import java.util.stream.Collectors;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.eventbus.EventBusConstants.*;
import static com.mindarray.nms.NMSConstants.*;

/**
 * Manages dependencies between IT components in the system.
 * <p>
 * The DependencyManager is responsible for:
 * <ul>
 *   <li>Storing and maintaining dependency relationships between components</li>
 *   <li>Adding, removing, and querying dependencies</li>
 *   <li>Managing dependency connections at different levels</li>
 *   <li>Handling special dependency types (e.g., Cisco UCS)</li>
 *   <li>Serializing and deserializing dependency data</li>
 * </ul>
 * <p>
 * This manager maintains a complex data structure (Map&lt;String, Map&lt;Byte, Set&lt;Integer&gt;&gt;&gt;)
 * to represent dependencies between entities at different levels. The outer map uses source IPs/targets
 * as keys, the middle map uses dependency levels as keys, and the inner set contains hash codes of
 * destination IPs/targets.
 * <p>
 * The dependency information stored by this manager is used by other components like
 * the AvailabilityCorrelationEngine and DependencyQueryProcessor to understand relationships
 * between components and make intelligent decisions based on those relationships.
 */

public class DependencyManager extends AbstractVerticle
{
    private static final Logger LOGGER = new Logger(DependencyManager.class, GlobalConstants.MOTADATA_AIOPS, "Dependency Manager");

    private final Map<String, Map<Byte, Set<Integer>>> localDomainDependencies = new HashMap<>(); // 0.1 million records with 5 level =  250-270 mb memory

    private final Map<String, Map<Byte, Set<Integer>>> crossDomainDependencies = new HashMap<>(); // 0.1 million records with 5 level = 250-270 mb memory

    private final Map<String, Integer> objectHashCodes = new HashMap<>(); // 1 million records = 80-100 mb memory

    private final Map<Integer, String> objectsByHashCode = new HashMap<>(); // 1 million records = 80-100 mb memory
    //    24666
    private final Map<String, JsonArray> contexts = Map.ofEntries(

            Map.entry(MetricPlugin.CISCO_VMANAGE.getName(), new JsonArray().add(new JsonObject().put(AIOpsConstants.DEPENDENCY_DEPENDENT_FIELD, "cisco.vmanage.control.connection").put(AIOpsConstants.DEPENDENCY_FILTER, "cisco.vmanage.control.connection.peer.type").put(AIOpsConstants.DEPENDENCY_DESTINATION, "cisco.vmanage.control.connection.peer"))),
            Map.entry(MetricPlugin.CISCO_ACI.getName(), new JsonArray().add(new JsonObject().put(AIOpsConstants.DEPENDENCY_SOURCE, "cisco.aci.leaf").put(AIOpsConstants.DEPENDENCY_SOURCE_PORT, "cisco.aci.leaf.interface").put(AIOpsConstants.DEPENDENCY_DESTINATION_PORT, "cisco.aci.leaf.connected.node.interface").put(AIOpsConstants.DEPENDENCY_FILTER, "cisco.aci.leaf.connected.node.role").put(AIOpsConstants.DEPENDENCY_DESTINATION, "cisco.aci.leaf.connected.node"))),
            Map.entry(MetricPlugin.CISCO_VEDGE_TUNNEL.getName(), new JsonArray().add(new JsonObject().put(AIOpsConstants.DEPENDENCY_DEPENDENT_FIELD, "cisco.vedge.tunnel").put(AIOpsConstants.DEPENDENCY_SOURCE, "cisco.vedge.tunnel.local.ip").put(AIOpsConstants.DEPENDENCY_SOURCE_PORT, "cisco.vedge.tunnel.local.color")
                    .put(AIOpsConstants.DEPENDENCY_DESTINATION, "cisco.vedge.tunnel.remote.ip").put(AIOpsConstants.DEPENDENCY_DESTINATION_PORT, "cisco.vedge.tunnel.remote.color"))),
            Map.entry(NMSConstants.MetricPlugin.VMWARE_ESXI_VM.getName(), new JsonArray().add(new JsonObject().put(AIOpsConstants.DEPENDENCY_DEPENDENT_FIELD, "esxi.vm").put(AIOpsConstants.DEPENDENCY_FILTER, "esxi.vm.ip"))),
            Map.entry(NMSConstants.MetricPlugin.HYPER_V_VM.getName(), new JsonArray().add(new JsonObject().put(AIOpsConstants.DEPENDENCY_DEPENDENT_FIELD, "hyperv.vm").put(AIOpsConstants.DEPENDENCY_FILTER, "hyperv.vm.ip"))),
            Map.entry(NMSConstants.MetricPlugin.CITRIX_XEN_VM.getName(), new JsonArray().add(new JsonObject().put(AIOpsConstants.DEPENDENCY_DEPENDENT_FIELD, "citrix.xen.vm").put(AIOpsConstants.DEPENDENCY_FILTER, "citrix.xen.vm.ip"))),
            Map.entry(NMSConstants.MetricPlugin.CISCO_UCS_FABRIC_INTERCONNECT.getName(), new JsonArray().add(new JsonObject().put(AIOpsConstants.DEPENDENCY_LEVEL, AIOpsConstants.DependencyLevel.TWELVE.getName()).put(AIOpsConstants.DEPENDENCY_DEPENDENT_FIELD, "cisco.ucs.fabric.interconnect.switch")
                    .put(AIOpsConstants.DEPENDENCY_SOURCE, "cisco.ucs.fabric.interconnect.switch").put(AIOpsConstants.DEPENDENCY_SOURCE_PORT, "cisco.ucs.fabric.interconnect.switch.port").put(AIOpsConstants.DEPENDENCY_DESTINATION, "cisco.ucs.fabric.interconnect.connected.peer").put(AIOpsConstants.DEPENDENCY_DESTINATION_PORT, "cisco.ucs.fabric.interconnect.connected.peer.port"))),
            Map.entry(NMSConstants.MetricPlugin.CISCO_UCS_RACK_MOUNT.getName(), new JsonArray().add(new JsonObject().put(AIOpsConstants.DEPENDENCY_LEVEL, AIOpsConstants.DependencyLevel.TEN.getName()).put(AIOpsConstants.DEPENDENCY_DEPENDENT_FIELD, "cisco.ucs.rack.mount.server.connection")
                    .put(AIOpsConstants.DEPENDENCY_SOURCE, "cisco.ucs.rack.mount.server.connection").put(AIOpsConstants.DEPENDENCY_SOURCE_PORT, "cisco.ucs.rack.mount.server.connection.port").put(AIOpsConstants.DEPENDENCY_DESTINATION, "cisco.ucs.rack.mount.server.peer").put(AIOpsConstants.DEPENDENCY_DESTINATION_PORT, "cisco.ucs.rack.mount.server.peer.port"))),
            Map.entry(NMSConstants.MetricPlugin.CISCO_WIRELESS_ACCESS_POINT.getName(), new JsonArray().add(new JsonObject().put(AIOpsConstants.DEPENDENCY_LEVEL, AIOpsConstants.DependencyLevel.TEN.getName()).put(AIOpsConstants.DEPENDENCY_DEPENDENT_FIELD, "cisco.wireless.access.point").put(AIOpsConstants.DEPENDENCY_FILTER, "cisco.wireless.access.point"))),
            Map.entry(NMSConstants.MetricPlugin.CISCO_WIRELESS_CLIENT.getName(), new JsonArray().add(new JsonObject().put(AIOpsConstants.DEPENDENCY_LEVEL, AIOpsConstants.DependencyLevel.SEVEN.getName()).put(AIOpsConstants.DEPENDENCY_DEPENDENT_FIELD, "cisco.wireless.client").put(AIOpsConstants.DEPENDENCY_FILTER, "cisco.wireless.client").put(AIOpsConstants.DEPENDENCY_DESTINATION, "cisco.wireless.client.ip.address"))),
            Map.entry(NMSConstants.MetricPlugin.RUCKUS_WIRELESS_ACCESS_POINT.getName(), new JsonArray().add(new JsonObject().put(AIOpsConstants.DEPENDENCY_LEVEL, AIOpsConstants.DependencyLevel.TEN.getName()).put(AIOpsConstants.DEPENDENCY_DEPENDENT_FIELD, "ruckus.wireless.access.point").put(AIOpsConstants.DEPENDENCY_FILTER, "ruckus.wireless.access.point"))),
            Map.entry(NMSConstants.MetricPlugin.RUCKUS_WIRELESS_CLIENT.getName(), new JsonArray().add(new JsonObject().put(AIOpsConstants.DEPENDENCY_LEVEL, AIOpsConstants.DependencyLevel.SEVEN.getName()).put(AIOpsConstants.DEPENDENCY_DEPENDENT_FIELD, "ruckus.wireless.client").put(AIOpsConstants.DEPENDENCY_FILTER, "ruckus.wireless.client.ip.address").put(AIOpsConstants.DEPENDENCY_DESTINATION, "ruckus.wireless.client.ip.address"))),
            Map.entry(NMSConstants.MetricPlugin.ARUBA_WIRELESS_ACCESS_POINT.getName(), new JsonArray().add(new JsonObject().put(AIOpsConstants.DEPENDENCY_LEVEL, AIOpsConstants.DependencyLevel.TEN.getName()).put(AIOpsConstants.DEPENDENCY_DEPENDENT_FIELD, "aruba.wireless.access.point").put(AIOpsConstants.DEPENDENCY_FILTER, "aruba.wireless.access.point"))),
            Map.entry(NMSConstants.MetricPlugin.ARUBA_WIRELESS_CLIENT.getName(), new JsonArray().add(new JsonObject().put(AIOpsConstants.DEPENDENCY_LEVEL, AIOpsConstants.DependencyLevel.SEVEN.getName()).put(AIOpsConstants.DEPENDENCY_DEPENDENT_FIELD, "aruba.wireless.client").put(AIOpsConstants.DEPENDENCY_FILTER, "aruba.wireless.client").put(AIOpsConstants.DEPENDENCY_DESTINATION, "aruba.wireless.client.ip.address"))),
            Map.entry(NMSConstants.MetricPlugin.AWS_CLOUD.getName(), new JsonArray().add(new JsonObject().put(AIOpsConstants.DEPENDENCY_DEPENDENT_FIELD, "aws.ec2").put(AIOpsConstants.DEPENDENCY_SOURCE, AWS_SERVICE_REGION).put(AIOpsConstants.DEPENDENCY_DESTINATION, AWS_SERVICE).put(AIOpsConstants.DEPENDENCY_FILTER, "aws.ec2.public.ip.address"))
                    .add(new JsonObject().put(AIOpsConstants.DEPENDENCY_DEPENDENT_FIELD, "aws.ebs").put(AIOpsConstants.DEPENDENCY_SOURCE, AWS_SERVICE_REGION).put(AIOpsConstants.DEPENDENCY_DESTINATION, AWS_SERVICE))
                    .add(new JsonObject().put(AIOpsConstants.DEPENDENCY_DEPENDENT_FIELD, "aws.rds").put(AIOpsConstants.DEPENDENCY_SOURCE, AWS_SERVICE_REGION).put(AIOpsConstants.DEPENDENCY_DESTINATION, AWS_SERVICE))
                    .add(new JsonObject().put(AIOpsConstants.DEPENDENCY_DEPENDENT_FIELD, "aws.elb").put(AIOpsConstants.DEPENDENCY_SOURCE, AWS_SERVICE_REGION).put(AIOpsConstants.DEPENDENCY_DESTINATION, AWS_SERVICE))
                    .add(new JsonObject().put(AIOpsConstants.DEPENDENCY_DEPENDENT_FIELD, "aws.dynamodb").put(AIOpsConstants.DEPENDENCY_SOURCE, AWS_SERVICE_REGION).put(AIOpsConstants.DEPENDENCY_DESTINATION, AWS_SERVICE))
                    .add(new JsonObject().put(AIOpsConstants.DEPENDENCY_DEPENDENT_FIELD, "aws.s3").put(AIOpsConstants.DEPENDENCY_SOURCE, AWS_SERVICE_REGION).put(AIOpsConstants.DEPENDENCY_DESTINATION, AWS_SERVICE))
                    .add(new JsonObject().put(AIOpsConstants.DEPENDENCY_DEPENDENT_FIELD, "aws.vpc").put(AIOpsConstants.DEPENDENCY_SOURCE, AWS_SERVICE_REGION).put(AIOpsConstants.DEPENDENCY_DESTINATION, AWS_SERVICE))
                    .add(new JsonObject().put(AIOpsConstants.DEPENDENCY_DEPENDENT_FIELD, "aws.cloudfront").put(AIOpsConstants.DEPENDENCY_SOURCE, AWS_SERVICE_REGION).put(AIOpsConstants.DEPENDENCY_DESTINATION, AWS_SERVICE))
                    .add(new JsonObject().put(AIOpsConstants.DEPENDENCY_DEPENDENT_FIELD, "aws.autoscaling").put(AIOpsConstants.DEPENDENCY_SOURCE, AWS_SERVICE_REGION).put(AIOpsConstants.DEPENDENCY_DESTINATION, AWS_SERVICE))
                    .add(new JsonObject().put(AIOpsConstants.DEPENDENCY_DEPENDENT_FIELD, "aws.lambda").put(AIOpsConstants.DEPENDENCY_SOURCE, AWS_SERVICE_REGION).put(AIOpsConstants.DEPENDENCY_DESTINATION, AWS_SERVICE))
                    .add(new JsonObject().put(AIOpsConstants.DEPENDENCY_DEPENDENT_FIELD, "aws.elastic.beanstalk").put(AIOpsConstants.DEPENDENCY_SOURCE, AWS_SERVICE_REGION).put(AIOpsConstants.DEPENDENCY_DESTINATION, AWS_SERVICE))
                    .add(new JsonObject().put(AIOpsConstants.DEPENDENCY_DEPENDENT_FIELD, "aws.documentdb").put(AIOpsConstants.DEPENDENCY_SOURCE, AWS_SERVICE_REGION).put(AIOpsConstants.DEPENDENCY_DESTINATION, AWS_SERVICE))
                    .add(new JsonObject().put(AIOpsConstants.DEPENDENCY_DEPENDENT_FIELD, "aws.sqs").put(AIOpsConstants.DEPENDENCY_SOURCE, AWS_SERVICE_REGION).put(AIOpsConstants.DEPENDENCY_DESTINATION, AWS_SERVICE))
                    .add(new JsonObject().put(AIOpsConstants.DEPENDENCY_DEPENDENT_FIELD, "aws.sns").put(AIOpsConstants.DEPENDENCY_SOURCE, AWS_SERVICE_REGION).put(AIOpsConstants.DEPENDENCY_DESTINATION, AWS_SERVICE))),
            Map.entry(NMSConstants.MetricPlugin.AZURE_CLOUD.getName(), new JsonArray().add(new JsonObject().put(AIOpsConstants.DEPENDENCY_DEPENDENT_FIELD, "azure.vm").put(AIOpsConstants.DEPENDENCY_SOURCE, AZURE_SERVICE_RESOURCE_GROUP).put(AIOpsConstants.DEPENDENCY_DESTINATION, AZURE_SERVICE).put(AIOpsConstants.DEPENDENCY_FILTER, "azure.vm.public.ip.address"))
                    .add(new JsonObject().put(AIOpsConstants.DEPENDENCY_DEPENDENT_FIELD, "azure.storage").put(AIOpsConstants.DEPENDENCY_SOURCE, AZURE_SERVICE_RESOURCE_GROUP).put(AIOpsConstants.DEPENDENCY_DESTINATION, AZURE_SERVICE))
                    .add(new JsonObject().put(AIOpsConstants.DEPENDENCY_DEPENDENT_FIELD, "azure.sql.database").put(AIOpsConstants.DEPENDENCY_SOURCE, AZURE_SERVICE_RESOURCE_GROUP).put(AIOpsConstants.DEPENDENCY_DESTINATION, AZURE_SERVICE))
                    .add(new JsonObject().put(AIOpsConstants.DEPENDENCY_DEPENDENT_FIELD, "azure.cosmos.db").put(AIOpsConstants.DEPENDENCY_SOURCE, AZURE_SERVICE_RESOURCE_GROUP).put(AIOpsConstants.DEPENDENCY_DESTINATION, AZURE_SERVICE))
                    .add(new JsonObject().put(AIOpsConstants.DEPENDENCY_DEPENDENT_FIELD, "azure.function").put(AIOpsConstants.DEPENDENCY_SOURCE, AZURE_SERVICE_RESOURCE_GROUP).put(AIOpsConstants.DEPENDENCY_DESTINATION, AZURE_SERVICE))
                    .add(new JsonObject().put(AIOpsConstants.DEPENDENCY_DEPENDENT_FIELD, "azure.cdn").put(AIOpsConstants.DEPENDENCY_SOURCE, AZURE_SERVICE_RESOURCE_GROUP).put(AIOpsConstants.DEPENDENCY_DESTINATION, AZURE_SERVICE))
                    .add(new JsonObject().put(AIOpsConstants.DEPENDENCY_DEPENDENT_FIELD, "azure.vmscaleset").put(AIOpsConstants.DEPENDENCY_SOURCE, AZURE_SERVICE_RESOURCE_GROUP).put(AIOpsConstants.DEPENDENCY_DESTINATION, AZURE_SERVICE))
                    .add(new JsonObject().put(AIOpsConstants.DEPENDENCY_DEPENDENT_FIELD, "azure.servicebus").put(AIOpsConstants.DEPENDENCY_SOURCE, AZURE_SERVICE_RESOURCE_GROUP).put(AIOpsConstants.DEPENDENCY_DESTINATION, AZURE_SERVICE))
                    .add(new JsonObject().put(AIOpsConstants.DEPENDENCY_DEPENDENT_FIELD, "azure.application.gateway").put(AIOpsConstants.DEPENDENCY_SOURCE, AZURE_SERVICE_RESOURCE_GROUP).put(AIOpsConstants.DEPENDENCY_DESTINATION, AZURE_SERVICE))
                    .add(new JsonObject().put(AIOpsConstants.DEPENDENCY_DEPENDENT_FIELD, "azure.loadbalancer").put(AIOpsConstants.DEPENDENCY_SOURCE, AZURE_SERVICE_RESOURCE_GROUP).put(AIOpsConstants.DEPENDENCY_DESTINATION, AZURE_SERVICE))
                    .add(new JsonObject().put(AIOpsConstants.DEPENDENCY_DEPENDENT_FIELD, "azure.mysql.server").put(AIOpsConstants.DEPENDENCY_SOURCE, AZURE_SERVICE_RESOURCE_GROUP).put(AIOpsConstants.DEPENDENCY_DESTINATION, AZURE_SERVICE))
                    .add(new JsonObject().put(AIOpsConstants.DEPENDENCY_DEPENDENT_FIELD, "azure.postgresql.server").put(AIOpsConstants.DEPENDENCY_SOURCE, AZURE_SERVICE_RESOURCE_GROUP).put(AIOpsConstants.DEPENDENCY_DESTINATION, AZURE_SERVICE))
                    .add(new JsonObject().put(AIOpsConstants.DEPENDENCY_DEPENDENT_FIELD, "azure.webapp").put(AIOpsConstants.DEPENDENCY_SOURCE, AZURE_SERVICE_RESOURCE_GROUP).put(AIOpsConstants.DEPENDENCY_DESTINATION, AZURE_SERVICE))),
            Map.entry(NMSConstants.MetricPlugin.WINDOWS_CLUSTER.getName(), new JsonArray().add(new JsonObject().put(AIOpsConstants.DEPENDENCY_LEVEL, AIOpsConstants.DependencyLevel.SEVEN.getName()).put(AIOpsConstants.DEPENDENCY_DEPENDENT_FIELD, "windows.cluster.node").put(AIOpsConstants.DEPENDENCY_FILTER, "windows.cluster.node"))),
            Map.entry(NMSConstants.MetricPlugin.CITRIX_XEN_CLUSTER.getName(), new JsonArray().add(new JsonObject().put(AIOpsConstants.DEPENDENCY_LEVEL, AIOpsConstants.DependencyLevel.NINE.getName()).put(AIOpsConstants.DEPENDENCY_DEPENDENT_FIELD, "citrix.xen.cluster.node").put(AIOpsConstants.DEPENDENCY_FILTER, "citrix.xen.cluster.node.ip"))
                    .add(new JsonObject().put(AIOpsConstants.DEPENDENCY_LEVEL, AIOpsConstants.DependencyLevel.EIGHT.getName()).put(AIOpsConstants.DEPENDENCY_DEPENDENT_FIELD, "citrix.xen.cluster.vm").put(AIOpsConstants.DEPENDENCY_FILTER, "citrix.xen.cluster.vm.ip").put(AIOpsConstants.DEPENDENCY_INSTANCE, "citrix.xen.cluster.vm.node.ip.address"))),
            Map.entry(NMSConstants.MetricPlugin.VCENTER.getName(), new JsonArray()
                    .add(new JsonObject().put(AIOpsConstants.DEPENDENCY_LEVEL, AIOpsConstants.DependencyLevel.NINE.getName()).put(AIOpsConstants.DEPENDENCY_DEPENDENT_FIELD, "vcenter.node").put(AIOpsConstants.DEPENDENCY_FILTER, "vcenter.node.ip").put(AIOpsConstants.DEPENDENCY_INSTANCE, "vcenter.node.cluster.name"))
                    .add(new JsonObject().put(AIOpsConstants.DEPENDENCY_LEVEL, AIOpsConstants.DependencyLevel.EIGHT.getName()).put(AIOpsConstants.DEPENDENCY_DEPENDENT_FIELD, "vcenter.vm").put(AIOpsConstants.DEPENDENCY_FILTER, "vcenter.vm.ip").put(AIOpsConstants.DEPENDENCY_INSTANCE, "vcenter.vm.server"))),
            Map.entry(NMSConstants.MetricPlugin.VCENTER_DATA_CENTER.getName(), new JsonArray()
                    .add(new JsonObject().put(AIOpsConstants.DEPENDENCY_LEVEL, AIOpsConstants.DependencyLevel.TWELVE.getName()).put(AIOpsConstants.DEPENDENCY_DEPENDENT_FIELD, "vcenter.datacenter").put(AIOpsConstants.DEPENDENCY_FILTER, "vcenter.datacenter"))),
            Map.entry(NMSConstants.MetricPlugin.VCENTER_CLUSTER.getName(), new JsonArray()
                    .add(new JsonObject().put(AIOpsConstants.DEPENDENCY_LEVEL, AIOpsConstants.DependencyLevel.TEN.getName()).put(AIOpsConstants.DEPENDENCY_DEPENDENT_FIELD, "vcenter.cluster").put(AIOpsConstants.DEPENDENCY_FILTER, "vcenter.cluster").put(AIOpsConstants.DEPENDENCY_INSTANCE, "vcenter.cluster.datacenter.name"))),
            Map.entry(MetricPlugin.PRISM.getName(), new JsonArray()
                    .add(new JsonObject().put(AIOpsConstants.DEPENDENCY_LEVEL, AIOpsConstants.DependencyLevel.NINE.getName()).put(AIOpsConstants.DEPENDENCY_DEPENDENT_FIELD, "prism.host").put(AIOpsConstants.DEPENDENCY_FILTER, "prism.host.ip").put(AIOpsConstants.DEPENDENCY_INSTANCE, "prism.host.cluster.name"))
                    .add(new JsonObject().put(AIOpsConstants.DEPENDENCY_LEVEL, AIOpsConstants.DependencyLevel.EIGHT.getName()).put(AIOpsConstants.DEPENDENCY_DEPENDENT_FIELD, "prism.vm").put(AIOpsConstants.DEPENDENCY_FILTER, "prism.vm.ip").put(AIOpsConstants.DEPENDENCY_INSTANCE, "prism.vm.host.ip"))),
            Map.entry(MetricPlugin.PRISM_CLUSTER.getName(), new JsonArray()
                    .add(new JsonObject().put(AIOpsConstants.DEPENDENCY_LEVEL, AIOpsConstants.DependencyLevel.TEN.getName()).put(AIOpsConstants.DEPENDENCY_DEPENDENT_FIELD, "prism.cluster").put(AIOpsConstants.DEPENDENCY_FILTER, "prism.cluster"))),
            Map.entry(MetricPlugin.NUTANIX_VM.getName(), new JsonArray()
                    .add(new JsonObject().put(AIOpsConstants.DEPENDENCY_LEVEL, AIOpsConstants.DependencyLevel.EIGHT.getName()).put(AIOpsConstants.DEPENDENCY_DEPENDENT_FIELD, "nutanix.vm").put(AIOpsConstants.DEPENDENCY_FILTER, "nutanix.vm.ip"))),
            Map.entry(NMSConstants.MetricPlugin.HYPER_V_CLUSTER.getName(), new JsonArray().add(new JsonObject().put(AIOpsConstants.DEPENDENCY_LEVEL, AIOpsConstants.DependencyLevel.NINE.getName()).put(AIOpsConstants.DEPENDENCY_DEPENDENT_FIELD, "hyperv.cluster.node").put(AIOpsConstants.DEPENDENCY_FILTER, "hyperv.cluster.node"))
                    .add(new JsonObject().put(AIOpsConstants.DEPENDENCY_LEVEL, AIOpsConstants.DependencyLevel.EIGHT.getName()).put(AIOpsConstants.DEPENDENCY_DEPENDENT_FIELD, "hyperv.cluster.vm").put(AIOpsConstants.DEPENDENCY_FILTER, "hyperv.cluster.vm.ip").put(AIOpsConstants.DEPENDENCY_INSTANCE, "hyperv.cluster.vm.server"))),
            Map.entry(NMSConstants.MetricPlugin.POSTGRESQL_SESSION.getName(), new JsonArray().add(new JsonObject().put(AIOpsConstants.DEPENDENCY_DEPENDENT_FIELD, "postgres.session").put(AIOpsConstants.DEPENDENCY_DESTINATION, "postgres.session.remote.client").put(AIOpsConstants.DEPENDENCY_FILTER, "postgres.session.application"))),
            Map.entry(NMSConstants.MetricPlugin.ORACLE_SESSION.getName(), new JsonArray().add(new JsonObject().put(AIOpsConstants.DEPENDENCY_DEPENDENT_FIELD, "oracle.session").put(AIOpsConstants.DEPENDENCY_DESTINATION, "oracle.session.remote.client")
                    .put(AIOpsConstants.DEPENDENCY_FILTER, "oracle.session.application"))),
            Map.entry(NMSConstants.MetricPlugin.SAP_HANA_SESSION.getName(), new JsonArray().add(new JsonObject().put(AIOpsConstants.DEPENDENCY_DEPENDENT_FIELD, "sap.hana.session").put(AIOpsConstants.DEPENDENCY_DESTINATION, "sap.hana.session.remote.client").put(AIOpsConstants.DEPENDENCY_FILTER, "sap.hana.session.application"))),
            Map.entry(NMSConstants.MetricPlugin.SQL_SERVER_SESSION.getName(), new JsonArray().add(new JsonObject().put(AIOpsConstants.DEPENDENCY_DEPENDENT_FIELD, "mssql.session").put(AIOpsConstants.DEPENDENCY_DESTINATION, "mssql.session.remote.client").put(AIOpsConstants.DEPENDENCY_FILTER, "mssql.session.application"))),
            Map.entry(NMSConstants.MetricPlugin.IBM_DB2_SESSION.getName(), new JsonArray().add(new JsonObject().put(AIOpsConstants.DEPENDENCY_DEPENDENT_FIELD, "db2.session").put(AIOpsConstants.DEPENDENCY_DESTINATION, "db2.session.remote.client").put(AIOpsConstants.DEPENDENCY_FILTER, "db2.session.application"))));

    private Integer hashCode = 0;
    private boolean writePending = false;
    private boolean dirty = false;

    /**
     * Initializes the DependencyManager and sets up event bus consumers.
     * <p>
     * This method sets up several event bus consumers:
     * <ul>
     *   <li>A consumer for change notifications to handle object deletions and cache updates</li>
     *   <li>A consumer for reading dependency data from disk</li>
     *   <li>A consumer for writing dependency data to disk</li>
     *   <li>Consumers for handling dependency operations (add, remove, query)</li>
     * </ul>
     * <p>
     * The method also deserializes existing dependency data from disk if available.
     *
     * @param promise Promise to be completed when initialization is done
     * @throws Exception If an error occurs during initialization
     */
    @Override
    public void start(Promise<Void> promise) throws Exception
    {
        try
        {
            vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_CHANGE_NOTIFICATION, message ->
            {
                var event = message.body();

                try
                {
                    if (event.containsKey(CHANGE_NOTIFICATION_TYPE))
                    {
                        if (ChangeNotificationType.valueOf(event.getString(CHANGE_NOTIFICATION_TYPE)) == ChangeNotificationType.DELETE_OBJECT && (!event.containsKey(METRIC_INSTANCES) || event.getValue(METRIC_INSTANCES) == null))
                        {
                            var object = event.getJsonObject(NMSConstants.OBJECT);

                            if (object != null && object.containsKey(AIOpsObject.OBJECT_CATEGORY) && !object.getString(AIOpsObject.OBJECT_CATEGORY).equalsIgnoreCase(NMSConstants.Category.SERVICE_CHECK.getName()))
                            {
                                remove(localDomainDependencies, object.getString(AIOpsObject.OBJECT_CATEGORY).equalsIgnoreCase(NMSConstants.Category.CLOUD.getName()) ? object.getString(AIOpsObject.OBJECT_TARGET) : object.getString(AIOpsObject.OBJECT_IP));

                                remove(crossDomainDependencies, object.getString(AIOpsObject.OBJECT_CATEGORY).equalsIgnoreCase(NMSConstants.Category.CLOUD.getName()) ? object.getString(AIOpsObject.OBJECT_TARGET) : object.getString(AIOpsObject.OBJECT_IP));
                            }
                        }
                        else if (EventBusConstants.ChangeNotificationType.valueOf(event.getString(CHANGE_NOTIFICATION_TYPE)) == ChangeNotificationType.UPDATE_CACHE)
                        {
                            if (dirty)
                            {
                                dirty = false;

                                HAConstants.notifyObserver(new JsonObject().put(HAConstants.CACHE_NAME, config().getString(EventBusConstants.EVENT_ROUTER_CONFIG)).put(RESULT, vertx.fileSystem().readFileBlocking(CURRENT_DIR + PATH_SEPARATOR + CONFIG_DIR + PATH_SEPARATOR + config().getString(EventBusConstants.EVENT_ROUTER_CONFIG))));
                            }
                        }
                    }
                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);
                }
            }).exceptionHandler(LOGGER::error);

            vertx.eventBus().<JsonObject>localConsumer(config().getString(EventBusConstants.EVENT_TYPE) + ".read", message ->
                    vertx.<JsonArray>executeBlocking(future ->
                    {
                        var event = message.body();

                        var filters = new JsonArray();

                        writePending = true;

                        LOGGER.info(String.format("dependency consumer started successfully for %s into %s", event.getString(EventBusConstants.EVENT_TYPE), config().getString(EventBusConstants.EVENT_TYPE)));

                        try
                        {
                            var file = CURRENT_DIR + PATH_SEPARATOR + CONFIG_DIR + PATH_SEPARATOR + EventBusConstants.replace(event.getString(EVENT_TYPE));

                            var buffer = vertx.fileSystem().existsBlocking(file) ? vertx.fileSystem().readFileBlocking(file) : null;

                            if (buffer != null && buffer.length() > 0)
                            {
                                deserialize(buffer.getBytes(), config().getString(EVENT_TYPE).contains("local") ? localDomainDependencies : crossDomainDependencies, filters);

                                LOGGER.info(String.format("dependencies for %s inserted successfully into %s", event.getString(EventBusConstants.EVENT_TYPE), config().getString(EVENT_TYPE)));
                            }
                        }
                        catch (Exception exception)
                        {
                            LOGGER.error(exception);
                        }

                        future.complete(filters);

                    }, false, result ->
                    {
                        writePending = false;

                        message.reply(result.result());

                    })).exceptionHandler(LOGGER::error);

            vertx.eventBus().<byte[]>localConsumer(config().getString(EVENT_TYPE) + ".write", message ->
            {
                LOGGER.info(String.format("writing dependencies into config for %s with flag %s", config().getString(EVENT_ROUTER_CONFIG), !writePending));

                if (!writePending)
                {
                    writePending = true;

                    vertx.<byte[]>executeBlocking(future ->
                    {
                        byte[] bytes = null;

                        try
                        {
                            if (!localDomainDependencies.isEmpty())
                            {
                                bytes = CodecUtil.serialize(transformDependencies(new HashMap<>(localDomainDependencies)));

                                if (bytes != null && bytes.length > 0)
                                {
                                    bytes = CodecUtil.compress(bytes);
                                }
                            }
                            else if (!crossDomainDependencies.isEmpty())
                            {
                                var dependencies = new HashMap<>(crossDomainDependencies).entrySet().parallelStream()
                                        .filter(key -> key.getKey().contains(VALUE_SEPARATOR) ? ObjectConfigStore.getStore().getItemByIP(key.getKey().split(VALUE_SEPARATOR_WITH_ESCAPE)[0].trim()) != null : ObjectConfigStore.getStore().getItemByIP(key.getKey()) != null)
                                        .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (e1, e2) -> e1, HashMap::new));

                                if (!dependencies.isEmpty())
                                {
                                    bytes = CodecUtil.serialize(transformDependencies(dependencies));

                                    if (bytes != null && bytes.length > 0)
                                    {
                                        bytes = CodecUtil.compress(bytes);
                                    }
                                }
                            }

                            if (bytes != null && bytes.length > 0)
                            {
                                LOGGER.info(String.format("updating new dependencies of %s bytes into config %s", bytes.length, config().getString(EVENT_ROUTER_CONFIG)));

                                vertx.fileSystem().writeFileBlocking(CURRENT_DIR + PATH_SEPARATOR + CONFIG_DIR + PATH_SEPARATOR + config().getString(EVENT_ROUTER_CONFIG), Buffer.buffer(bytes));

                                LOGGER.info(String.format("new dependencies updated into config %s", config().getString(EVENT_ROUTER_CONFIG)));
                            }
                            else
                            {
                                if (CommonUtil.traceEnabled())
                                {
                                    LOGGER.trace(String.format("%s to write dependency for %s ", ErrorMessageConstants.ITEM_NOT_FOUND, config().getString(EVENT_ROUTER_CONFIG)));
                                }
                            }
                        }
                        catch (Exception exception)
                        {
                            LOGGER.error(exception);
                        }

                        future.complete(bytes);

                    }, false, result ->
                    {
                        message.reply(result.result());

                        writePending = false;
                    });
                }
                else
                {
                    writePending = false;

                    message.reply(null);
                }
            }).exceptionHandler(LOGGER::error);

            vertx.eventBus().<JsonObject>localConsumer(config().getString(EventBusConstants.EVENT_TYPE) + ".query", message ->
            {
                var result = new JsonObject();

                var event = message.body();

                try
                {
                    query(AIOpsConstants.DependencyType.LOCAL_DOMAIN.getName(), event, localDomainDependencies, result);

                    query(AIOpsConstants.DependencyType.CROSS_DOMAIN.getName(), event, crossDomainDependencies, result);
                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);
                }

                if (CommonUtil.traceEnabled())
                {
                    LOGGER.trace(String.format("query %s executing in %s engine with result %s", event, config().getString(EVENT_TYPE), result));
                }

                message.reply(result);

            }).exceptionHandler(LOGGER::error);

            vertx.eventBus().<JsonObject>localConsumer(config().getString(EVENT_TYPE), message ->
            {
                try
                {
                    if (message.body() != null && !message.body().isEmpty())
                    {
                        var event = message.body();

                        var source = event.getString(AIOpsConstants.DEPENDENCY_SOURCE);

                        var level = AIOpsConstants.DependencyLevel.valueOfName(CommonUtil.getByteValue(event.getValue(AIOpsConstants.DEPENDENCY_LEVEL)));

                        if (event.getString(AIOpsConstants.DEPENDENCY_TYPE).equalsIgnoreCase(AIOpsConstants.DependencyType.LOCAL_DOMAIN.getName()))
                        {
                            localDomainDependencies.computeIfAbsent(source, value -> new HashMap<>());

                            var connections = localDomainDependencies.get(source);

                            switch (AIOpsConstants.DependencyOperation.valueOfName(event.getString(AIOpsConstants.DEPENDENCY_OPERATION)))
                            {
                                case ADD -> //for process/service -> app mapping and for dependency mapping update time add dependency

                                        add(source, level, event);
                                case ADD_MULTIPLES ->
                                { //for process/interface/vm's add after metric provision/rediscovery

                                    clear(level.getName(), connections);

                                    connections.computeIfAbsent(level.getName(), value -> new TreeSet<>()).addAll(event.getJsonArray(OBJECTS).stream().map(item -> getHashCode(JsonObject.mapFrom(item).getString(AIOpsObject.OBJECT_NAME))).collect(Collectors.toSet()));

                                    dirty = true;
                                }

                                case REMOVE -> // for removing application with source + separator + process/service name

                                        remove(event, source, level, localDomainDependencies, connections);
                                case REMOVE_MULTIPLES -> // for removing process / process->App / interface

                                        removeMultiples(event, source, level, localDomainDependencies, connections);
                                case REMOVE_PARENT ->
                                {
                                    // #24881 - (Manual connection will be persisted)
                                    var existingChildren = event.getJsonArray(DependencyMapper.EXISTING_CHILDREN);

                                    // Topology scheduler : if parent is ********** then any connection like "**********_|@#|_5" exist, then we'll remove. (#26769)
                                    // new connections will be added on 'ADD_MAP' operation.

                                    if (connections.containsKey(AIOpsConstants.DependencyLevel.FOUR.getName()))
                                    {
                                        for (var hashCode : connections.get(AIOpsConstants.DependencyLevel.FOUR.getName()))
                                        {
                                            if (!existingChildren.contains(objectsByHashCode.get(hashCode)))
                                            {
                                                localDomainDependencies.remove(source + VALUE_SEPARATOR + objectsByHashCode.get(hashCode));

                                                dirty = true;
                                            }
                                        }
                                    }

                                    // remove parent from child like ********** -> 172.16.10.43 where ********** is parent and 172.16.10.43 is child,
                                    // so in that case 172.16.10.43 contains -1 and where value is ********** that we need to remove
                                    if (connections.containsKey(AIOpsConstants.DependencyLevel.SIX.getName()))
                                    {
                                        for (var hashCode : connections.get(AIOpsConstants.DependencyLevel.SIX.getName()))
                                        {
                                            var localDomainConnections = localDomainDependencies.get(objectsByHashCode.get(hashCode));

                                            if (localDomainConnections != null && localDomainConnections.containsKey(level.getName()))
                                            {
                                                dirty = true;

                                                localDomainConnections.get(level.getName()).removeIf(integer -> source.equalsIgnoreCase(objectsByHashCode.get(integer)));
                                            }
                                        }
                                    }
                                }
                                case ADD_MAP ->
                                { //for mapping relationship with polling / topology scanning contexts

                                    if (event.containsKey(EVENT_TYPE) && event.getString(EVENT_TYPE).equalsIgnoreCase(EVENT_TOPOLOGY))
                                    {
                                        // network -> interface -> neighbor mapping
                                        var neighbors = event.getJsonArray(RESULT);

                                        if (neighbors != null)
                                        {
                                            clear(level.getName(), connections);

                                            for (var index = 0; index < neighbors.size(); index++)
                                            {
                                                var neighbor = neighbors.getJsonObject(index);

                                                var destination = neighbor.getString(AIOpsConstants.DEPENDENCY_DESTINATION);

                                                add(source, level, event
                                                        .put(AIOpsConstants.DEPENDENCY_DESTINATION, destination)
                                                        .put(AIOpsConstants.DEPENDENCY_PARENT, neighbor.getString(AIOpsConstants.DEPENDENCY_PARENT))
                                                        .put(AIOpsConstants.DEPENDENCY_SOURCE_PORT, neighbor.getString(AIOpsConstants.DEPENDENCY_SOURCE_PORT))
                                                        .put(AIOpsConstants.DEPENDENCY_DESTINATION_PORT, neighbor.getString(AIOpsConstants.DEPENDENCY_DESTINATION_PORT))
                                                        .put(AIOpsConstants.DEPENDENCY_DEPENDENT_FIELD, neighbor.getString(AIOpsConstants.DEPENDENCY_SOURCE_PORT)));

                                                if (CommonUtil.isNotNullOrEmpty(neighbor.getString(AIOpsConstants.DEPENDENCY_DESTINATION_PORT))
                                                        && event.containsKey(AIOpsConstants.DEPENDENCY_LEVEL)
                                                        && AIOpsConstants.DependencyLevel.SIX.getName().equals(CommonUtil.getByteValue(event.getValue(AIOpsConstants.DEPENDENCY_LEVEL))))
                                                {
                                                    event.remove(AIOpsConstants.DEPENDENCY_DEPENDENT_FIELD);

                                                    localDomainDependencies.computeIfAbsent(destination, value -> new HashMap<>());

                                                    add(destination, level, event.put(AIOpsConstants.DEPENDENCY_SOURCE, destination)
                                                            .put(AIOpsConstants.DEPENDENCY_DESTINATION, source)
                                                            .put(AIOpsConstants.DEPENDENCY_SOURCE_PORT, neighbor.getString(AIOpsConstants.DEPENDENCY_DESTINATION_PORT))
                                                            .put(AIOpsConstants.DEPENDENCY_DESTINATION_PORT, neighbor.getString(AIOpsConstants.DEPENDENCY_SOURCE_PORT))
                                                            .put(AIOpsConstants.DEPENDENCY_DEPENDENT_FIELD, neighbor.getString(AIOpsConstants.DEPENDENCY_DESTINATION_PORT)));
                                                }
                                            }
                                        }
                                    }

                                    else if (event.getJsonObject(RESULT) != null)
                                    {
                                        JsonArray items;

                                        var metricPlugin = MetricPlugin.valueOfName(event.getString(Metric.METRIC_PLUGIN));

                                        switch (metricPlugin)
                                        {
                                            case CISCO_UCS_CHASSIS ->
                                                    add("cisco.ucs.chassis", event.getJsonObject(RESULT), AIOpsConstants.DependencyLevel.NINE, connections);

                                            case CISCO_UCS_FABRIC_INTERCONNECT ->
                                            {
                                                add("cisco.ucs.fabric.interconnect", event.getJsonObject(RESULT), AIOpsConstants.DependencyLevel.TWELVE, connections);

                                                addCiscoUCSDependency(metricPlugin, event, source, connections);
                                            }

                                            case CISCO_UCS_RACK_MOUNT ->
                                            {
                                                add("cisco.ucs.rack.mount.fex", event.getJsonObject(RESULT), AIOpsConstants.DependencyLevel.EIGHT, connections);

                                                add("cisco.ucs.rack.mount.server", event.getJsonObject(RESULT), AIOpsConstants.DependencyLevel.TEN, connections);

                                                addCiscoUCSDependency(metricPlugin, event, source, connections);
                                            }

                                            case AWS_CLOUD, AZURE_CLOUD ->
                                            {
                                                for (var index = 0; index < contexts.get(metricPlugin.getName()).size(); index++)
                                                {
                                                    var context = contexts.get(metricPlugin.getName()).getJsonObject(index);

                                                    items = event.getJsonObject(RESULT).getJsonArray(context.getString(AIOpsConstants.DEPENDENCY_DEPENDENT_FIELD));

                                                    if (items != null)
                                                    {
                                                        for (var itemIndex = 0; itemIndex < items.size(); itemIndex++)
                                                        {
                                                            var item = items.getJsonObject(itemIndex);

                                                            level = AIOpsConstants.DependencyLevel.TEN;

                                                            connections.computeIfAbsent(level.getName(), value -> new TreeSet<>()).add(getHashCode(item.getString(context.getString(AIOpsConstants.DEPENDENCY_SOURCE))));

                                                            level = AIOpsConstants.DependencyLevel.EIGHT;

                                                            connections.computeIfAbsent(level.getName(), value -> new TreeSet<>()).add(getHashCode(item.getString(context.getString(AIOpsConstants.DEPENDENCY_DESTINATION))));

                                                            var key = source + VALUE_SEPARATOR + item.getString(context.getString(AIOpsConstants.DEPENDENCY_SOURCE));

                                                            localDomainDependencies.computeIfAbsent(key, value -> new HashMap<>());

                                                            var localDomainConnections = localDomainDependencies.get(key);

                                                            localDomainConnections.computeIfAbsent(level.getName(), value -> new TreeSet<>()).add(getHashCode(item.getString(context.getString(AIOpsConstants.DEPENDENCY_DESTINATION))));

                                                            dirty = true;

                                                            key = item.getString(context.getString(AIOpsConstants.DEPENDENCY_DESTINATION));

                                                            if (!localDomainDependencies.containsKey(key))
                                                            {
                                                                localDomainDependencies.put(key, new HashMap<>());

                                                                //need to add instance entry with router in local event router
                                                                vertx.eventBus().send(EVENT_DEPENDENCY + "." + AIOpsConstants.DependencyType.LOCAL_DOMAIN.getName() + ".add.context",
                                                                        new JsonObject().put(TARGET, key).put(ENGINE_TYPE, config().getString(EVENT_TYPE)));
                                                            }

                                                            localDomainConnections = localDomainDependencies.get(key);

                                                            localDomainConnections.computeIfAbsent(AIOpsConstants.DependencyLevel.MINUS_ONE.getName(), value -> new TreeSet<>()).add(getHashCode(source));

                                                            // add region/resource group in connected instances .. as of now not seeing any use cases for it
                                                                    /*localDependency.computeIfAbsent(AIOpsConstants.DependencyLevel.ONE.getName(), value -> new TreeSet<>());

                                                                    localDependency.get(AIOpsConstants.DependencyLevel.ONE.getName()).add(getHashCode(item.getString(context.getString(AIOpsConstants.DEPENDENCY_SOURCE))));*/

                                                            if (context.getString(AIOpsConstants.DEPENDENCY_FILTER) != null && CommonUtil.isNotNullOrEmpty(item.getString(context.getString(AIOpsConstants.DEPENDENCY_FILTER)))
                                                                    && (ObjectConfigStore.getStore().getItemByIP(item.getString(context.getString(AIOpsConstants.DEPENDENCY_FILTER))) != null || ObjectConfigStore.getStore().getItemByTarget(item.getString(context.getString(AIOpsConstants.DEPENDENCY_FILTER))) != null))
                                                            {
                                                                level = AIOpsConstants.DependencyLevel.SEVEN;

                                                                key = source + VALUE_SEPARATOR + item.getString(context.getString(AIOpsConstants.DEPENDENCY_DESTINATION));

                                                                add(item, context, key, item.getString(context.getString(AIOpsConstants.DEPENDENCY_SOURCE)), level);

                                                                level = AIOpsConstants.DependencyLevel.TWELVE;

                                                                localDomainConnections = localDomainDependencies.get(key);

                                                                localDomainConnections.computeIfAbsent(level.getName(), value -> new TreeSet<>()).add(getHashCode(source));
                                                            }
                                                        }
                                                    }
                                                }
                                            }

                                            case RUCKUS_WIRELESS_CLIENT, CISCO_WIRELESS_CLIENT, ARUBA_WIRELESS_CLIENT ->
                                            {
                                                for (var index = 0; index < contexts.get(metricPlugin.getName()).size(); index++)
                                                {
                                                    var context = contexts.get(metricPlugin.getName()).getJsonObject(index);

                                                    items = event.getJsonObject(RESULT).getJsonArray(context.getString(AIOpsConstants.DEPENDENCY_DEPENDENT_FIELD));

                                                    if (items != null)
                                                    {
                                                        level = AIOpsConstants.DependencyLevel.valueOfName(CommonUtil.getByteValue(context.getValue(AIOpsConstants.DEPENDENCY_LEVEL)));

                                                        for (var itemIndex = 0; itemIndex < items.size(); itemIndex++)
                                                        {
                                                            var item = items.getJsonObject(itemIndex);

                                                            if (CommonUtil.isNotNullOrEmpty(item.getString(context.getString(AIOpsConstants.DEPENDENCY_FILTER)))
                                                                    && ObjectConfigStore.getStore().getItemByIP(item.getString(context.getString(AIOpsConstants.DEPENDENCY_FILTER))) != null)
                                                            {
                                                                add(item, context, source + VALUE_SEPARATOR + item.getString(context.getString(AIOpsConstants.DEPENDENCY_DEPENDENT_FIELD)), source, level);
                                                            }
                                                        }
                                                    }
                                                }
                                            }

                                            // windows cluster dependency is same as wireless
                                            case CISCO_WIRELESS_ACCESS_POINT, RUCKUS_WIRELESS_ACCESS_POINT,
                                                 ARUBA_WIRELESS_ACCESS_POINT, WINDOWS_CLUSTER ->
                                            {
                                                for (var index = 0; index < contexts.get(metricPlugin.getName()).size(); index++)
                                                {
                                                    var context = contexts.get(metricPlugin.getName()).getJsonObject(index);

                                                    items = event.getJsonObject(RESULT).getJsonArray(context.getString(AIOpsConstants.DEPENDENCY_DEPENDENT_FIELD));

                                                    if (items != null)
                                                    {
                                                        level = AIOpsConstants.DependencyLevel.valueOfName(CommonUtil.getByteValue(context.getValue(AIOpsConstants.DEPENDENCY_LEVEL)));

                                                        clear(level.getName(), connections);

                                                        connections.computeIfAbsent(level.getName(), value -> new TreeSet<>());

                                                        dirty = true;

                                                        var entries = connections.get(level.getName());

                                                        for (var itemIndex = 0; itemIndex < items.size(); itemIndex++)
                                                        {
                                                            var item = items.getJsonObject(itemIndex);

                                                            if (CommonUtil.isNotNullOrEmpty(item.getString(context.getString(AIOpsConstants.DEPENDENCY_FILTER))))
                                                            {
                                                                entries.add(getHashCode(item.getString(context.getString(AIOpsConstants.DEPENDENCY_FILTER))));

                                                            }
                                                        }
                                                    }
                                                }
                                            }

                                            case HYPER_V_CLUSTER, CITRIX_XEN_CLUSTER, VCENTER, VCENTER_CLUSTER,
                                                 VCENTER_DATA_CENTER, PRISM_CLUSTER, PRISM ->
                                            {
                                                for (var index = 0; index < contexts.get(metricPlugin.getName()).size(); index++)
                                                {
                                                    var context = contexts.get(metricPlugin.getName()).getJsonObject(index);

                                                    items = event.getJsonObject(RESULT).getJsonArray(context.getString(AIOpsConstants.DEPENDENCY_DEPENDENT_FIELD));

                                                    if (items != null && !items.isEmpty())
                                                    {
                                                        level = AIOpsConstants.DependencyLevel.valueOfName(CommonUtil.getByteValue(context.getValue(AIOpsConstants.DEPENDENCY_LEVEL)));

                                                        clear(level.getName(), connections);

                                                        connections.computeIfAbsent(level.getName(), value -> new TreeSet<>());

                                                        dirty = true;

                                                        var entries = connections.get(level.getName());

                                                        for (var itemIndex = 0; itemIndex < items.size(); itemIndex++)
                                                        {
                                                            var item = items.getJsonObject(itemIndex);

                                                            if (CommonUtil.isNotNullOrEmpty(item.getString(context.getString(AIOpsConstants.DEPENDENCY_FILTER))))
                                                            {
                                                                entries.add(getHashCode(item.getString(context.getString(AIOpsConstants.DEPENDENCY_FILTER))));

                                                                if (context.containsKey(AIOpsConstants.DEPENDENCY_INSTANCE) && CommonUtil.isNotNullOrEmpty(item.getString(context.getString(AIOpsConstants.DEPENDENCY_INSTANCE))))
                                                                {
                                                                    var key = source + VALUE_SEPARATOR + item.getString(context.getString(AIOpsConstants.DEPENDENCY_INSTANCE));

                                                                    localDomainDependencies.computeIfAbsent(key, value -> new HashMap<>());

                                                                    var localDomainConnections = localDomainDependencies.get(key);

                                                                    localDomainConnections.computeIfAbsent(level.getName(), value -> new TreeSet<>()).add(getHashCode(item.getString(context.getString(AIOpsConstants.DEPENDENCY_FILTER))));

                                                                    localDomainConnections.computeIfAbsent(AIOpsConstants.DependencyLevel.MINUS_ONE.getName(), value -> new TreeSet<>()).add(getHashCode(item.getString(context.getString(AIOpsConstants.DEPENDENCY_INSTANCE))));
                                                                }
                                                            }
                                                        }
                                                    }
                                                }
                                            }

                                            case VMWARE_ESXI_VM, HYPER_V_VM, CITRIX_XEN_VM, NUTANIX_VM ->
                                            {
                                                clear(AIOpsConstants.DependencyLevel.NINE.getName(), connections);

                                                clear(AIOpsConstants.DependencyLevel.EIGHT.getName(), connections);

                                                clear(AIOpsConstants.DependencyLevel.SEVEN.getName(), connections);

                                                connections.computeIfAbsent(AIOpsConstants.DependencyLevel.EIGHT.getName(), value -> new TreeSet<>());

                                                dirty = true;

                                                var entries = connections.get(AIOpsConstants.DependencyLevel.EIGHT.getName());

                                                for (var index = 0; index < contexts.get(metricPlugin.getName()).size(); index++)
                                                {
                                                    var context = contexts.get(metricPlugin.getName()).getJsonObject(index);

                                                    items = event.getJsonObject(RESULT).getJsonArray(context.getString(AIOpsConstants.DEPENDENCY_DEPENDENT_FIELD));

                                                    if (items != null)
                                                    {
                                                        for (var itemIndex = 0; itemIndex < items.size(); itemIndex++)
                                                        {
                                                            var item = items.getJsonObject(itemIndex);

//                                                                        #24640

                                                            var vm = CommonUtil.isNotNullOrEmpty(item.getString(context.getString(AIOpsConstants.DEPENDENCY_FILTER))) ? item.getString(context.getString(AIOpsConstants.DEPENDENCY_FILTER))
                                                                    : item.getString(context.getString(AIOpsConstants.DEPENDENCY_DEPENDENT_FIELD));

                                                            entries.add(getHashCode(vm));

                                                            if (CommonUtil.isNotNullOrEmpty(item.getString(context.getString(AIOpsConstants.DEPENDENCY_FILTER)))
                                                                    && ObjectConfigStore.getStore().getItemByIP(item.getString(context.getString(AIOpsConstants.DEPENDENCY_FILTER))) != null
                                                                    && !ObjectConfigStore.getStore().getItemsByType(Type.PRISM).contains(ObjectConfigStore.getStore().getItemByIP(item.getString(context.getString(AIOpsConstants.DEPENDENCY_FILTER)))))
                                                            {
                                                                // above prism condition is for prism/nutanix topology.
                                                                // controller vm will have same ip as prism. if don't put condition then it will pass condition and add dependency as server in nutanix and will be shown as server connected to nutanix server.

                                                                level = AIOpsConstants.DependencyLevel.SEVEN;

                                                                connections.computeIfAbsent(level.getName(), value -> new TreeSet<>()).add(getHashCode(item.getString(context.getString(AIOpsConstants.DEPENDENCY_FILTER))));

                                                                var key = source + VALUE_SEPARATOR + vm;

                                                                localDomainDependencies.put(key, new HashMap<>());

                                                                var localDomainConnections = localDomainDependencies.get(key);

                                                                clear(level.getName(), localDomainConnections);

                                                                localDomainConnections.computeIfAbsent(level.getName(), value -> new TreeSet<>()).add(getHashCode(item.getString(context.getString(AIOpsConstants.DEPENDENCY_FILTER))));

                                                                // esxi add on child key (esxi + separator + vm => level -2 == esxi)
                                                                            /*level = AIOpsConstants.DependencyLevel.TWO;

                                                                            clear(key, level, localDomainDependencies);

                                                                            localDomainDependencies.get(key).computeIfAbsent(level.getName(),value -> new TreeSet<>());

                                                                            localDomainDependencies.get(key).get(level.getName()).add(getHashCode(source));*/
                                                            }
                                                        }
                                                    }
                                                }

                                            }

                                            case CISCO_VMANAGE ->
                                            {
                                                // manager will have controller,bond (level 10) and edge router on (level 7)

                                                clear(AIOpsConstants.DependencyLevel.SEVEN.getName(), connections);

                                                clear(AIOpsConstants.DependencyLevel.TEN.getName(), connections);

                                                for (var index = 0; index < contexts.get(metricPlugin.getName()).size(); index++)
                                                {
                                                    var context = contexts.get(metricPlugin.getName()).getJsonObject(index);

                                                    items = event.getJsonObject(RESULT).getJsonArray(context.getString(AIOpsConstants.DEPENDENCY_DEPENDENT_FIELD));

                                                    if (items != null)
                                                    {
                                                        for (var itemIndex = 0; itemIndex < items.size(); itemIndex++)
                                                        {
                                                            var item = items.getJsonObject(itemIndex);

                                                            if (CommonUtil.isNotNullOrEmpty(item.getString(context.getString(AIOpsConstants.DEPENDENCY_FILTER))) && CommonUtil.isNotNullOrEmpty(item.getString(context.getString(AIOpsConstants.DEPENDENCY_DESTINATION))))
                                                            {
                                                                level = item.getString(context.getString(AIOpsConstants.DEPENDENCY_FILTER)).equalsIgnoreCase("vedge") ? AIOpsConstants.DependencyLevel.SEVEN : AIOpsConstants.DependencyLevel.TEN;

                                                                connections.computeIfAbsent(level.getName(), value -> new TreeSet<>());

                                                                connections.get(level.getName()).add(getHashCode(item.getString(context.getString(AIOpsConstants.DEPENDENCY_DESTINATION))));
                                                            }
                                                        }
                                                    }
                                                }
                                            }

                                            case CISCO_VEDGE_TUNNEL ->
                                            {
                                                // each edge router will store color/interfaces(level 4) and connected edge router(level 7)
                                                // each edge router with tunnel will have connected edge router with tunnel. same as network.

                                                connections.computeIfAbsent(AIOpsConstants.DependencyLevel.FOUR.getName(), value -> new TreeSet<>());

                                                // firstly remove connection using color/interfaces and then add new fresh connection
                                                for (var hashCode : connections.get(AIOpsConstants.DependencyLevel.FOUR.getName()))
                                                {
                                                    localDomainDependencies.remove(source + VALUE_SEPARATOR + objectsByHashCode.get(hashCode));
                                                }

                                                clear(AIOpsConstants.DependencyLevel.FOUR.getName(), connections);

                                                clear(AIOpsConstants.DependencyLevel.SEVEN.getName(), connections);

                                                for (var index = 0; index < contexts.get(metricPlugin.getName()).size(); index++)
                                                {
                                                    var context = contexts.get(metricPlugin.getName()).getJsonObject(index);

                                                    items = event.getJsonObject(RESULT).getJsonArray(context.getString(AIOpsConstants.DEPENDENCY_DEPENDENT_FIELD));

                                                    if (items != null)
                                                    {
                                                        for (var itemIndex = 0; itemIndex < items.size(); itemIndex++)
                                                        {
                                                            var item = items.getJsonObject(itemIndex);

                                                            if (CommonUtil.isNotNullOrEmpty(item.getString(context.getString(AIOpsConstants.DEPENDENCY_SOURCE_PORT))))
                                                            {
                                                                level = AIOpsConstants.DependencyLevel.FOUR;

                                                                connections.computeIfAbsent(level.getName(), value -> new TreeSet<>());

                                                                connections.get(level.getName()).add(getHashCode(item.getString(context.getString(AIOpsConstants.DEPENDENCY_SOURCE_PORT))));

                                                                if (CommonUtil.isNotNullOrEmpty(item.getString(context.getString(AIOpsConstants.DEPENDENCY_DESTINATION))) &&
                                                                        CommonUtil.isNotNullOrEmpty(item.getString(context.getString(AIOpsConstants.DEPENDENCY_DESTINATION_PORT))))
                                                                {
                                                                    level = AIOpsConstants.DependencyLevel.SEVEN;

                                                                    connections.computeIfAbsent(level.getName(), value -> new TreeSet<>());

                                                                    connections.get(level.getName()).add(getHashCode(item.getString(context.getString(AIOpsConstants.DEPENDENCY_DESTINATION))));

                                                                    var key = source + VALUE_SEPARATOR + item.getString(context.getString(AIOpsConstants.DEPENDENCY_SOURCE_PORT));

                                                                    localDomainDependencies.computeIfAbsent(key, value -> new HashMap<>()).computeIfAbsent(level.getName(), value -> new TreeSet<>())
                                                                            .add(getHashCode(item.getString(context.getString(AIOpsConstants.DEPENDENCY_DESTINATION)) + VALUE_SEPARATOR + item.getString(context.getString(AIOpsConstants.DEPENDENCY_DESTINATION_PORT))));
                                                                }
                                                            }
                                                        }
                                                    }
                                                }
                                            }

                                            case CISCO_ACI ->
                                            {
                                               /*
                                                 APIC, leaf, and spine will each be stored at level 7
                                                   (Object IP > [7] leaf)

                                                 Endpoints will be stored at level 6
                                                   (leaf > [6] endpoint)

                                                 Interfaces will be stored at level 4
                                                   (leaf > [4] interface > [7] spine/APIC)
                                                */
                                                clear(AIOpsConstants.DependencyLevel.SEVEN.getName(), connections);

                                                for (var index = 0; index < contexts.get(metricPlugin.getName()).size(); index++)
                                                {
                                                    var context = contexts.get(metricPlugin.getName()).getJsonObject(index);

                                                    items = event.getJsonObject(RESULT).getJsonArray(context.getString(AIOpsConstants.DEPENDENCY_SOURCE));

                                                    if (items != null)
                                                    {
                                                        // Clearing leaf dependencies at levels 4 and 6
                                                        for (var itemIndex = 0; itemIndex < items.size(); itemIndex++)
                                                        {
                                                            localDomainDependencies.remove(source + VALUE_SEPARATOR + items.getJsonObject(itemIndex).getString(context.getString(AIOpsConstants.DEPENDENCY_SOURCE)) + VALUE_SEPARATOR + "leaf");
                                                        }

                                                        for (var itemIndex = 0; itemIndex < items.size(); itemIndex++)
                                                        {
                                                            var item = items.getJsonObject(itemIndex);

                                                            if (CommonUtil.isNotNullOrEmpty(item.getString(context.getString(AIOpsConstants.DEPENDENCY_FILTER))) && CommonUtil.isNotNullOrEmpty(item.getString(context.getString(AIOpsConstants.DEPENDENCY_DESTINATION)))
                                                                    && CommonUtil.isNotNullOrEmpty(item.getString(context.getString(AIOpsConstants.DEPENDENCY_SOURCE))))
                                                            {
                                                                // Storing endpoints directly at level 6 with the leaf as the source
                                                                if (item.getString(context.getString(AIOpsConstants.DEPENDENCY_FILTER)).equalsIgnoreCase("endpoint"))
                                                                {
                                                                    localDomainDependencies.computeIfAbsent(source + VALUE_SEPARATOR + item.getString(context.getString(AIOpsConstants.DEPENDENCY_SOURCE)) + VALUE_SEPARATOR + "leaf", value -> new HashMap<>()).computeIfAbsent(AIOpsConstants.DependencyLevel.SIX.getName(), value -> new TreeSet<>())
                                                                            .add(getHashCode(item.getString(context.getString(AIOpsConstants.DEPENDENCY_DESTINATION)) + VALUE_SEPARATOR + item.getString(context.getString(AIOpsConstants.DEPENDENCY_FILTER))));

                                                                }

                                                                // storing leaf directly at level 7 with object IP as source
                                                                else if (item.getString(context.getString(AIOpsConstants.DEPENDENCY_FILTER)).equalsIgnoreCase("leaf"))
                                                                {
                                                                    level = AIOpsConstants.DependencyLevel.SEVEN;

                                                                    connections.computeIfAbsent(level.getName(), value -> new TreeSet<>());

                                                                    connections.get(level.getName()).add(getHashCode(item.getString(context.getString(AIOpsConstants.DEPENDENCY_SOURCE)) + VALUE_SEPARATOR + item.getString(context.getString(AIOpsConstants.DEPENDENCY_FILTER))));
                                                                }

                                                                // only spine and apic connections will have source and destination interfaces.
                                                                else if (CommonUtil.isNotNullOrEmpty(item.getString(context.getString(AIOpsConstants.DEPENDENCY_SOURCE_PORT))) && CommonUtil.isNotNullOrEmpty(item.getString(context.getString(AIOpsConstants.DEPENDENCY_DESTINATION_PORT))))
                                                                {
                                                                    // storing interfaces directly at level 4 with leaf as source
                                                                    var key = source + VALUE_SEPARATOR + item.getString(context.getString(AIOpsConstants.DEPENDENCY_SOURCE)) + VALUE_SEPARATOR + "leaf";

                                                                    localDomainDependencies.computeIfAbsent(key, value -> new HashMap<>()).computeIfAbsent(AIOpsConstants.DependencyLevel.FOUR.getName(), value -> new TreeSet<>())
                                                                            .add(getHashCode(item.getString(context.getString(AIOpsConstants.DEPENDENCY_SOURCE_PORT))));

                                                                    // storing spine/apic at level 7 with leaf+interface as source
                                                                    key = item.getString(context.getString(AIOpsConstants.DEPENDENCY_SOURCE)) + VALUE_SEPARATOR + "leaf" + VALUE_SEPARATOR + item.getString(context.getString(AIOpsConstants.DEPENDENCY_SOURCE_PORT));

                                                                    //need to add instance entry with router in local event router
                                                                    vertx.eventBus().send(EVENT_DEPENDENCY + "." + AIOpsConstants.DependencyType.LOCAL_DOMAIN.getName() + ".add.context",
                                                                            new JsonObject().put(TARGET, key).put(ENGINE_TYPE, config().getString(EVENT_TYPE)));

                                                                    localDomainDependencies.put(key, new HashMap<>());

                                                                    localDomainDependencies.computeIfAbsent(key, value -> new HashMap<>()).computeIfAbsent(AIOpsConstants.DependencyLevel.SEVEN.getName(), value -> new TreeSet<>())
                                                                            .add(getHashCode(item.getString(context.getString(AIOpsConstants.DEPENDENCY_DESTINATION)) + VALUE_SEPARATOR + item.getString(context.getString(AIOpsConstants.DEPENDENCY_FILTER)) + VALUE_SEPARATOR + item.getString(context.getString(AIOpsConstants.DEPENDENCY_DESTINATION_PORT))));

                                                                }
                                                            }
                                                        }
                                                    }
                                                }
                                            }

                                            default ->
                                            {
                                            }
                                        }
                                    }
                                }
                                default ->
                                {
                                }
                            }
                        }
                        else
                        {
                            crossDomainDependencies.computeIfAbsent(source, value -> new HashMap<>());

                            var connections = crossDomainDependencies.get(source);

                            if (CommonUtil.isNotNullOrEmpty(event.getString(AIOpsConstants.DEPENDENCY_OPERATION)))
                            {
                                switch (AIOpsConstants.DependencyOperation.valueOfName(event.getString(AIOpsConstants.DEPENDENCY_OPERATION)))
                                {
                                    case REMOVE -> remove(event, source, level, crossDomainDependencies, connections);

                                    case REMOVE_MULTIPLES ->
                                            removeMultiples(event, source, level, crossDomainDependencies, connections);

                                    case ADD_MAP ->
                                    {
                                        var metricPlugin = MetricPlugin.valueOfName(event.getString(Metric.METRIC_PLUGIN));

                                        switch (metricPlugin)
                                        {
                                            case LINUX_PROCESS, WINDOWS_PROCESS, IBM_AIX_PROCESS, HP_UX_PROCESS,
                                                 SOLARIS_PROCESS ->
                                            {
                                                var neighbors = event.getJsonObject(RESULT).getJsonArray("system.process.network.connection");

                                                if (neighbors != null && !neighbors.isEmpty())
                                                {
                                                    add(SystemProcess.SYSTEM_PROCESS, event.getJsonObject(RESULT), AIOpsConstants.DependencyLevel.FOUR, connections);

                                                    for (var index = 0; index < neighbors.size(); index++)
                                                    {
                                                        var neighbor = neighbors.getJsonObject(index);

                                                        if (CommonUtil.isNotNullOrEmpty(neighbor.getString("system.process.source.ip")) && !neighbor.getString("system.process.source.ip").equalsIgnoreCase(neighbor.getString(SYSTEM_PROCESS_DESTINATION_IP))
                                                                && ObjectConfigStore.getStore().getItemByIP(neighbor.getString(SYSTEM_PROCESS_DESTINATION_IP)) != null)
                                                        {
                                                            level = AIOpsConstants.DependencyLevel.FOUR;

                                                            connections.computeIfAbsent(level.getName(), value -> new TreeSet<>()).add(getHashCode(neighbor.getString(SystemProcess.SYSTEM_PROCESS)));

                                                            dirty = true;

                                                            level = AIOpsConstants.DependencyLevel.SEVEN;

                                                            var key = source + VALUE_SEPARATOR + neighbor.getString(SystemProcess.SYSTEM_PROCESS);

                                                            crossDomainDependencies.computeIfAbsent(key, value -> new HashMap<>());

                                                            crossDomainDependencies.get(key).computeIfAbsent(level.getName(), value -> new TreeSet<>()).add(getHashCode(neighbor.getString(SYSTEM_PROCESS_DESTINATION_IP)));
                                                        }
                                                    }
                                                }
                                            }

                                            case POSTGRESQL_SESSION, SAP_HANA_SESSION, SQL_SERVER_SESSION,
                                                 IBM_DB2_SESSION, ORACLE_SESSION ->
                                            {
                                                for (var index = 0; index < contexts.get(metricPlugin.getName()).size(); index++)
                                                {
                                                    var context = contexts.get(metricPlugin.getName()).getJsonObject(index);

                                                    var items = event.getJsonObject(RESULT).getJsonArray(context.getString(AIOpsConstants.DEPENDENCY_DEPENDENT_FIELD));

                                                    if (items != null)
                                                    {
                                                        for (var itemIndex = 0; itemIndex < items.size(); itemIndex++)
                                                        {
                                                            var item = items.getJsonObject(itemIndex);

                                                            if (CommonUtil.isNotNullOrEmpty(item.getString(context.getString(AIOpsConstants.DEPENDENCY_DESTINATION))) &&
                                                                    (ObjectConfigStore.getStore().getItemByIP(item.getString(context.getString(AIOpsConstants.DEPENDENCY_DESTINATION))) != null
                                                                            || ObjectConfigStore.getStore().getIdByObjectName(item.getString(context.getString(AIOpsConstants.DEPENDENCY_DESTINATION))) != null)) //in session plugin we are not getting ip so need host name condition also
                                                            {
                                                                //TODO do we need to clear data??? this will also clear flow dependencies data
                                                                clear(AIOpsConstants.DependencyLevel.SEVEN.getName(), connections);

                                                                connections.computeIfAbsent(AIOpsConstants.DependencyLevel.SEVEN.getName(), value -> new TreeSet<>()).add(getHashCode(item.getString(context.getString(AIOpsConstants.DEPENDENCY_DESTINATION))));

                                                                dirty = true;

                                                                if (CommonUtil.isNotNullOrEmpty(item.getString(context.getString(AIOpsConstants.DEPENDENCY_FILTER))))
                                                                {
                                                                    clear(AIOpsConstants.DependencyLevel.FOUR.getName(), connections);

                                                                    connections.computeIfAbsent(AIOpsConstants.DependencyLevel.FOUR.getName(), value -> new TreeSet<>()).add(getHashCode(item.getString(context.getString(AIOpsConstants.DEPENDENCY_FILTER))));

                                                                    var key = source + VALUE_SEPARATOR + item.getString(context.getString(AIOpsConstants.DEPENDENCY_FILTER));

                                                                    crossDomainDependencies.computeIfAbsent(key, value -> new HashMap<>());

                                                                    var crossDomainConnections = crossDomainDependencies.get(key);

                                                                    crossDomainConnections.computeIfAbsent(AIOpsConstants.DependencyLevel.SEVEN.getName(), value -> new TreeSet<>()).add(getHashCode(item.getString(context.getString(AIOpsConstants.DEPENDENCY_DESTINATION))));
                                                                }
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                            default ->
                                            {
                                            }
                                        }
                                    }

                                    default ->
                                    {
                                        //do nothing
                                    }
                                }
                            }
                            else // flow/packet agent based dependencies
                            {
                                connections.computeIfAbsent(level.getName(), value -> new TreeSet<>()).add(getHashCode(event.getString(AIOpsConstants.DEPENDENCY_DEPENDENT_FIELD))); //port mapping

                                var key = source + VALUE_SEPARATOR + event.getString(AIOpsConstants.DEPENDENCY_DEPENDENT_FIELD); // source + separator + port => level -4 == destination

                                level = AIOpsConstants.DependencyLevel.SEVEN; // add server/network os

                                crossDomainDependencies.computeIfAbsent(key, value -> new HashMap<>());

                                crossDomainDependencies.get(key).computeIfAbsent(level.getName(), value -> new TreeSet<>()).add(getHashCode(event.getString(AIOpsConstants.DEPENDENCY_DESTINATION)));

                                dirty = true;
                            }
                        }
                    }
                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);
                }
            }).exceptionHandler(LOGGER::error);

            LOGGER.debug(String.format("%s deployed successfully.....", config().getString(EventBusConstants.EVENT_TYPE)));

            promise.complete();
        }

        catch (Exception exception)
        {
            LOGGER.error(exception);

            if (!promise.future().isComplete())
            {
                promise.fail(exception);
            }
        }
    }

    /**
     * Removes multiple dependencies based on the event parameters.
     * <p>
     * This method handles the removal of multiple dependencies, particularly for complex
     * relationships like network-to-server mappings. It can selectively remove specific
     * dependencies while preserving others, based on the filter and dependent field in the event.
     * <p>
     * For example, if a source device has connections to multiple destinations through the same
     * interface, and only one destination is being deleted, this method will preserve the
     * connections to the other destinations.
     * <p>
     * This method also sets the dirty flag to true to indicate that the dependency data
     * has been modified and should be persisted to disk.
     *
     * @param event        The event containing the dependency information to remove
     * @param source       The source of the dependencies to remove
     * @param level        The dependency level
     * @param dependencies The dependency map to remove from
     * @param connections  The connection map for the source
     */
    private void removeMultiples(JsonObject event, String source, AIOpsConstants.DependencyLevel level, Map<String, Map<Byte, Set<Integer>>> dependencies, Map<Byte, Set<Integer>> connections)
    {
        LOGGER.info(String.format("removing source %s with filter %s and dependent field %s", source, event.getString(AIOpsConstants.DEPENDENCY_FILTER), event.getString(AIOpsConstants.DEPENDENCY_DEPENDENT_FIELD)));

        // for mapping like network -> server and in that case if we delete server monitor need to remove key from network also
        if (CommonUtil.isNotNullOrEmpty(event.getString(AIOpsConstants.DEPENDENCY_FILTER)))
        {
            if (CommonUtil.isNotNullOrEmpty(event.getString(AIOpsConstants.DEPENDENCY_DEPENDENT_FIELD)))
            {
                // if source monitor have different device connected with same instance/interface
                // then don't remove whole link. instead, remove link for only device which is deleted.
                // ex : 12.2 with 10111 interface have two device connected
                // 1) 10.210 2) 13.25
                // if 13.25 device gets deleted then 12.2's link with 10.210 get persisted

                var localDependencies = dependencies.get(source + VALUE_SEPARATOR + event.getString(AIOpsConstants.DEPENDENCY_FILTER));

                if (localDependencies != null)
                {
                    var valid = true;

                    var hashCode = getHashCode(event.getString(AIOpsConstants.DEPENDENCY_DEPENDENT_FIELD));

                    for (var dependency : localDependencies.entrySet())
                    {
                        if (AIOpsConstants.DependencyLevel.valueOfName(dependency.getKey()) == AIOpsConstants.DependencyLevel.SIX
                                || AIOpsConstants.DependencyLevel.valueOfName(dependency.getKey()) == AIOpsConstants.DependencyLevel.FIVE)
                        {
                            localDependencies.get(dependency.getKey()).remove(hashCode);

                            // need to check if there is other dependency exist then need to don't remove whole link

                            if (!localDependencies.get(dependency.getKey()).isEmpty())
                            {
                                valid = false;
                            }
                        }
                    }

                    if (valid)
                    {
                        dependencies.remove(source + VALUE_SEPARATOR + event.getString(AIOpsConstants.DEPENDENCY_FILTER));

                        LOGGER.info("no other connected device/link found so removing whole link");
                    }
                }
            }
            else
            {
                dependencies.remove(source + VALUE_SEPARATOR + event.getString(AIOpsConstants.DEPENDENCY_FILTER));
            }

            dirty = true;
        }
        else if (CommonUtil.isNotNullOrEmpty(event.getString(AIOpsConstants.DEPENDENCY_DEPENDENT_FIELD)))
        {
            dependencies.remove(source + VALUE_SEPARATOR + event.getString(AIOpsConstants.DEPENDENCY_DEPENDENT_FIELD));

            dirty = true;
        }

        clear(connections, level.getName(), event.getString(AIOpsConstants.DEPENDENCY_DEPENDENT_FIELD));

        clear(source, dependencies);

    }

    /**
     * Removes a single dependency based on the event parameters.
     * <p>
     * This method removes a dependency between the source and the dependent field
     * specified in the event. It also clears the dependency at the specified level
     * in the connections map.
     * <p>
     * This method is simpler than removeMultiples as it doesn't need to handle
     * complex cases where a source has multiple connections through the same interface.
     * <p>
     * This method also sets the dirty flag to true to indicate that the dependency data
     * has been modified and should be persisted to disk.
     *
     * @param event        The event containing the dependency information to remove
     * @param source       The source of the dependency to remove
     * @param level        The dependency level
     * @param dependencies The dependency map to remove from
     * @param connections  The connection map for the source
     */
    private void remove(JsonObject event, String source, AIOpsConstants.DependencyLevel level, Map<String, Map<Byte, Set<Integer>>> dependencies, Map<Byte, Set<Integer>> connections)
    {
        LOGGER.info(String.format("removing source %s with filter %s and dependent field %s", source, event.getString(AIOpsConstants.DEPENDENCY_FILTER), event.getString(AIOpsConstants.DEPENDENCY_DEPENDENT_FIELD)));

        dependencies.remove(source + VALUE_SEPARATOR + event.getString(AIOpsConstants.DEPENDENCY_DEPENDENT_FIELD));

        clear(connections, level.getName(), event.getString(AIOpsConstants.DEPENDENCY_FILTER));

        dirty = true;
    }

    /**
     * Clears a specific dependency at a level.
     * <p>
     * This method removes a specific dependency (identified by filter) at the specified
     * level from the connections map. If removing the dependency results in an empty set
     * at that level, the level is also removed from the connections map.
     * <p>
     * This method also sets the dirty flag to true if a level is removed to indicate that
     * the dependency data has been modified and should be persisted to disk.
     *
     * @param connections The connection map to clear from
     * @param level       The dependency level to clear at
     * @param filter      The filter identifying the specific dependency to clear
     */
    private void clear(Map<Byte, Set<Integer>> connections, Byte level, String filter)
    {
        var entries = connections.get(level);

        if (entries != null && filter != null)
        {
            entries.remove(getHashCode(filter));

            if (entries.isEmpty())
            {
                connections.remove(level);

                dirty = true;
            }
        }
    }

    // clear by map key -> ip/source

    /**
     * Clears all dependencies for a source if they are empty.
     * <p>
     * This method checks if the dependencies for a source are empty (i.e., have no connections
     * at any level), and if so, removes the source from the dependencies map.
     * <p>
     * This method also sets the dirty flag to true if a source is removed to indicate that
     * the dependency data has been modified and should be persisted to disk.
     *
     * @param source       The source to check and potentially clear
     * @param dependencies The dependency map to clear from
     */
    private void clear(String source, Map<String, Map<Byte, Set<Integer>>> dependencies)
    {
        var exist = false;

        var connections = dependencies.get(source);

        for (var entry : AIOpsConstants.DependencyLevel.values())
        {
            if (connections.get(entry.getName()) != null && !connections.get(entry.getName()).isEmpty())
            {
                exist = true;

                break;
            }
        }

        // need this for manual dependency of network update as any time when customer did changes in manual dependency we have to clear old one but we can't remove that link connection as may be same link has l2 and l3 connectivity and user can do only one at a time
        // remove source + separator + connected link
        if (!exist)
        {
            dependencies.remove(source);

            dirty = true;
        }
    }

    private void addCiscoUCSDependency(MetricPlugin plugin, JsonObject event, String source, Map<Byte, Set<Integer>> connections)
    {
        for (var index = 0; index < contexts.get(plugin.getName()).size(); index++)
        {
            var context = contexts.get(plugin.getName()).getJsonObject(index);

            var items = event.getJsonObject(GlobalConstants.RESULT).getJsonArray(context.getString(AIOpsConstants.DEPENDENCY_DEPENDENT_FIELD));

            if (items != null)
            {
                connections.computeIfAbsent(AIOpsConstants.DependencyLevel.FOUR.getName(), value -> new TreeSet<>());

                for (var itemIndex = 0; itemIndex < items.size(); itemIndex++)
                {
                    var item = items.getJsonObject(itemIndex);

                    var dependentSource = CommonUtil.getString(item.getValue(context.getString(AIOpsConstants.DEPENDENCY_SOURCE_PORT)));

                    var dependentDestination = CommonUtil.getString(item.getValue(context.getString(AIOpsConstants.DEPENDENCY_DESTINATION_PORT)));

                    if (CommonUtil.isNotNullOrEmpty(item.getString(context.getString(AIOpsConstants.DEPENDENCY_SOURCE))) && CommonUtil.isNotNullOrEmpty(item.getString(context.getString(AIOpsConstants.DEPENDENCY_DESTINATION)))
                            && CommonUtil.isNotNullOrEmpty(dependentSource) && CommonUtil.isNotNullOrEmpty(dependentDestination))
                    {
                        var key = source + VALUE_SEPARATOR + item.getString(context.getString(AIOpsConstants.DEPENDENCY_SOURCE));

                        var level = getCiscoUCSDependencyLevel(item.getString(context.getString(AIOpsConstants.DEPENDENCY_DESTINATION)).toLowerCase());

                        if (level == null)
                        {
                            level = AIOpsConstants.DependencyLevel.valueOfName(CommonUtil.getByteValue(context.getValue(AIOpsConstants.DEPENDENCY_LEVEL)));
                        }

                        localDomainDependencies.computeIfAbsent(key, value -> new HashMap<>());

                        var localDomainConnections = localDomainDependencies.get(key);

                        localDomainConnections.computeIfAbsent(level.getName(), value -> new TreeSet<>()).add(getHashCode(dependentSource + GlobalConstants.VALUE_SEPARATOR + item.getString(context.getString(AIOpsConstants.DEPENDENCY_DESTINATION))));

                        key = source + VALUE_SEPARATOR + item.getString(context.getString(AIOpsConstants.DEPENDENCY_DESTINATION));

                        localDomainDependencies.computeIfAbsent(key, value -> new HashMap<>());

                        localDomainConnections = localDomainDependencies.get(key);

                        level = getCiscoUCSDependencyLevel(item.getString(context.getString(AIOpsConstants.DEPENDENCY_SOURCE)).toLowerCase());

                        if (level == null)
                        {
                            level = AIOpsConstants.DependencyLevel.valueOfName(CommonUtil.getByteValue(context.getValue(AIOpsConstants.DEPENDENCY_LEVEL)));
                        }

                        localDomainConnections.computeIfAbsent(level.getName(), value -> new TreeSet<>()).add(getHashCode(dependentDestination + GlobalConstants.VALUE_SEPARATOR + item.getString(context.getString(AIOpsConstants.DEPENDENCY_SOURCE))));

                        connections.get(AIOpsConstants.DependencyLevel.FOUR.getName()).add(getHashCode(dependentSource));

                        connections.get(AIOpsConstants.DependencyLevel.FOUR.getName()).add(getHashCode(dependentDestination));
                    }
                }
            }
        }
    }

    private AIOpsConstants.DependencyLevel getCiscoUCSDependencyLevel(String field)
    {
        AIOpsConstants.DependencyLevel level = null;

        if (field.contains("fex"))
        {
            level = AIOpsConstants.DependencyLevel.EIGHT;
        }
        else if (field.contains("rack"))
        {
            level = AIOpsConstants.DependencyLevel.TEN;
        }
        else if (field.contains("chassis"))
        {
            level = AIOpsConstants.DependencyLevel.NINE;
        }
        else if (field.contains("fabric"))
        {
            level = AIOpsConstants.DependencyLevel.TWELVE;
        }

        return level;
    }

    /**
     * Queries dependencies for a given source and adds them to the result.
     * <p>
     * This method retrieves dependency information for the specified source from the dependency map,
     * transforms the internal hash code representation to string values, and adds the dependencies
     * to the result object. It also recursively includes dependencies of dependencies if they exist
     * and match the requested dependency levels.
     *
     * @param dependencyType The type of dependency (local.domain or cross.domain)
     * @param event          The event containing query parameters, including the dependency source
     * @param dependencies   The dependency map to query (localDomainDependencies or crossDomainDependencies)
     * @param result         The result object to populate with dependency information
     */
    private void query(String dependencyType, JsonObject event, Map<String, Map<Byte, Set<Integer>>> dependencies, JsonObject result)
    {
        var connections = dependencies.get(event.getString(AIOpsConstants.DEPENDENCY_SOURCE));

        if (connections != null && !connections.isEmpty())
        {
            var qualifiedDependencies = new HashMap<String, Map<Byte, Set<String>>>();

            var transformedConnections = transformConnections(connections);

            if (!transformedConnections.isEmpty())
            {
                qualifiedDependencies.put(event.getString(AIOpsConstants.DEPENDENCY_SOURCE), transformedConnections);

                var levels = event.getJsonArray(AIOpsConstants.DEPENDENCY_LEVEL);

                if (levels == null)
                {
                    levels = new JsonArray();

                    for (var level : AIOpsConstants.DependencyLevel.values())
                    {
                        levels.add(level.getName());
                    }
                }

                for (var level : levels)
                {
                    if (transformedConnections.get(CommonUtil.getByteValue(level)) != null)
                    {
                        for (var object : transformedConnections.get(CommonUtil.getByteValue(level)))
                        {
                            var key = event.getString(AIOpsConstants.DEPENDENCY_SOURCE) + VALUE_SEPARATOR + object;

                            if (dependencies.containsKey(key))
                            {
                                //24639
                                var items = transformConnections(dependencies.get(key));

                                if (!items.isEmpty())
                                {
                                    qualifiedDependencies.put(key, items);
                                }
                            }
                        }
                    }
                }
            }

            result.put(dependencyType, JsonObject.mapFrom(qualifiedDependencies));
        }
    }

    /**
     * Deserializes dependency data from a byte array into the dependency map.
     * <p>
     * This method takes serialized dependency data, deserializes it, and populates
     * the dependency map with the deserialized data. It converts the string representation
     * of dependencies to an internal hash code representation for efficient storage and lookup.
     * <p>
     * The method also populates a filters array with unique source identifiers that don't
     * contain a value separator, which are typically top-level sources.
     *
     * @param bytes        The serialized dependency data as a byte array
     * @param dependencies The dependency map to populate with the deserialized data
     * @param filters      A JsonArray to populate with unique source identifiers
     */
    private void deserialize(byte[] bytes, Map<String, Map<Byte, Set<Integer>>> dependencies, JsonArray filters)
    {
        try
        {
            var entries = (Map<String, Map<Byte, List<String>>>) CodecUtil.deserialize(CodecUtil.toBytes(bytes), Map.class);

            if (entries != null)
            {
                for (var entry : entries.entrySet())
                {
                    var result = entry.getKey();

                    var dependency = new HashMap<Byte, Set<Integer>>();

                    dependencies.put(result, dependency);

                    if (!result.contains(VALUE_SEPARATOR) && !filters.contains(result))
                    {
                        filters.add(result);
                    }

                    for (var level : entry.getValue().entrySet())
                    {
                        var value = CommonUtil.getByteValue(level.getKey());

                        dependency.put(value, new TreeSet<>());

                        for (var object : level.getValue())
                        {
                            dependency.get(value).add(getHashCode(object));
                        }
                    }
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    /**
     * Transforms the internal hash code representation of dependencies to string values.
     * <p>
     * This method converts the entire dependency map from the internal hash code representation
     * to a string representation suitable for serialization or returning in API responses.
     * It creates a new map with the same structure but with string values instead of hash codes.
     *
     * @param dependencies The dependency map with hash code values to transform
     * @return A new dependency map with string values instead of hash codes
     */
    private Map<String, Map<Byte, Set<String>>> transformDependencies(Map<String, Map<Byte, Set<Integer>>> dependencies)
    {
        var transformedDependencies = new HashMap<String, Map<Byte, Set<String>>>();

        if (dependencies != null && !dependencies.isEmpty())
        {
            for (var entry : dependencies.entrySet())
            {
                var dependency = entry.getKey();

                transformedDependencies.put(dependency, new HashMap<>());

                for (var level : entry.getValue().entrySet())
                {
                    transformedDependencies.get(dependency).put(level.getKey(), new TreeSet<>());

                    level.getValue().forEach(hash -> transformedDependencies.get(dependency).get(level.getKey()).add(objectsByHashCode.get(hash)));
                }
            }
        }

        return transformedDependencies;
    }

    /**
     * Transforms a single connection map from hash codes to string values.
     * <p>
     * This method converts a map of dependency connections for a single source from
     * the internal hash code representation to a string representation suitable for
     * serialization or returning in API responses. It creates a new map with the same
     * structure but with string values instead of hash codes.
     * <p>
     * This method also sets the dirty flag to true to indicate that the dependency data
     * has been modified and should be persisted to disk.
     *
     * @param connections The connection map with hash code values to transform
     * @return A new connection map with string values instead of hash codes
     */
    private Map<Byte, Set<String>> transformConnections(Map<Byte, Set<Integer>> connections)
    {
        var transformedConnections = new HashMap<Byte, Set<String>>();

        if (connections != null && !connections.isEmpty())
        {
            for (var entry : connections.entrySet())
            {
                transformedConnections.put(entry.getKey(), new TreeSet<>());

                entry.getValue().forEach(hash -> transformedConnections.get(entry.getKey()).add(objectsByHashCode.get(hash)));
            }

            dirty = true;
        }

        return transformedConnections;
    }

    /**
     * Adds dependencies from a result object to a connection map.
     * <p>
     * This method extracts objects from a result JsonObject using the specified filter,
     * converts them to hash codes, and adds them to the connection map at the specified
     * dependency level. It clears any existing dependencies at that level before adding
     * the new ones.
     * <p>
     * This method also sets the dirty flag to true to indicate that the dependency data
     * has been modified and should be persisted to disk.
     *
     * @param filter      The filter to use for extracting objects from the result
     * @param result      The result JsonObject containing the objects to add
     * @param level       The dependency level at which to add the objects
     * @param connections The connection map to add the dependencies to
     */
    private void add(String filter, JsonObject result, AIOpsConstants.DependencyLevel level, Map<Byte, Set<Integer>> connections)
    {
        var objects = result.getJsonArray(filter);

        if (objects != null)
        {
            clear(level.getName(), connections);

            connections.computeIfAbsent(level.getName(), value -> new TreeSet<>());

            var entries = connections.get(level.getName());

            for (var index = 0; index < objects.size(); index++)
            {
                entries.add(getHashCode(CommonUtil.getString(objects.getJsonObject(index).getValue(filter))));
            }

            dirty = true;
        }
    }

    /**
     * Adds a dependency between a source and destination to the local domain dependencies.
     * <p>
     * This method adds a dependency between the specified source (key) and destination
     * at the specified dependency level. It creates the necessary maps and sets if they
     * don't exist, and adds the destination hash code to the appropriate set.
     * <p>
     * This method also sets the dirty flag to true to indicate that the dependency data
     * has been modified and should be persisted to disk.
     *
     * @param result      The result JsonObject (not used in this method)
     * @param item        The item JsonObject containing the destination
     * @param key         The source key to add the dependency to
     * @param destination The destination field in the item to get the destination value from
     * @param level       The dependency level at which to add the dependency
     */
    private void add(JsonObject result, JsonObject item, String key, String destination, AIOpsConstants.DependencyLevel level)
    {
        localDomainDependencies.computeIfAbsent(key, value -> new HashMap<>());

        var localDomainConnections = localDomainDependencies.get(key);

        localDomainConnections.put(level.getName(), new TreeSet<>());

        localDomainConnections.get(level.getName()).add(getHashCode(result.getString(item.getString(AIOpsConstants.DEPENDENCY_FILTER))));

        localDomainConnections.put(AIOpsConstants.DependencyLevel.TEN.getName(), new TreeSet<>());

        localDomainConnections.get(AIOpsConstants.DependencyLevel.TEN.getName()).add(getHashCode(destination));

        dirty = true;
    }

    /**
     * Adds dependencies from an event to the appropriate dependency map.
     * <p>
     * This method processes a dependency event and adds the dependencies to either
     * the local domain or cross domain dependency map based on the dependency type.
     * It handles different dependency operations (add, add_multiples, add_map) and
     * different types of dependencies (network, virtualization, cloud, etc.).
     * <p>
     * For add_multiples operation, it processes multiple dependencies at once.
     * For add_map operation, it processes dependencies with additional context.
     * For regular add operation, it adds a single dependency.
     *
     * @param source The source of the dependency
     * @param level  The dependency level
     * @param event  The event containing the dependency information
     */
    private void add(String source, AIOpsConstants.DependencyLevel level, JsonObject event)
    {
        if (CommonUtil.debugEnabled())
        {
            LOGGER.debug(String.format("add request came for %s of %s level, dependent field %s", source, level, event.getString(AIOpsConstants.DEPENDENCY_DEPENDENT_FIELD)));
        }

        var localDomainConnections = localDomainDependencies.get(source);

        localDomainConnections.computeIfAbsent(level.getName(), value -> new TreeSet<>()).add(getHashCode(event.getString(AIOpsConstants.DEPENDENCY_DESTINATION)));

        dirty = true;

        //for service rediscover application we map application direct to object and in service case dependent field is null
        // second and third condition is for network topology if in scanning we find any neighbor with interface 1 but we are not monitor interface 1 in that case we will not build dependent dependencies (source + "" + dependentfield)
        if (event.getString(AIOpsConstants.DEPENDENCY_DEPENDENT_FIELD) != null && localDomainDependencies.get(source).get(AIOpsConstants.DependencyLevel.FOUR.getName()) != null
                && localDomainDependencies.get(source).get(AIOpsConstants.DependencyLevel.FOUR.getName()).contains(getHashCode(event.getString(AIOpsConstants.DEPENDENCY_DEPENDENT_FIELD))))
        {
            var key = source + GlobalConstants.VALUE_SEPARATOR + event.getString(AIOpsConstants.DEPENDENCY_DEPENDENT_FIELD);

            localDomainDependencies.computeIfAbsent(key, value -> new HashMap<>());

            localDomainConnections = localDomainDependencies.get(key);

            var destination = event.getString(AIOpsConstants.DEPENDENCY_DESTINATION);

            if (CommonUtil.isNotNullOrEmpty(event.getString(AIOpsConstants.DEPENDENCY_DESTINATION_PORT)))
            {
                destination += VALUE_SEPARATOR + event.getString(AIOpsConstants.DEPENDENCY_DESTINATION_PORT);
            }

            localDomainConnections.computeIfAbsent(level.getName(), value -> new TreeSet<>()).add(getHashCode(destination));

            if (CommonUtil.debugEnabled())
            {
                LOGGER.debug(String.format("key : %s added for destination %s", key, destination));
            }

            if (CommonUtil.isNotNullOrEmpty(event.getString(AIOpsConstants.DEPENDENCY_PARENT)))
            {
                level = AIOpsConstants.DependencyLevel.MINUS_ONE;

                localDomainConnections.computeIfAbsent(level.getName(), value -> new TreeSet<>()).add(getHashCode(event.getString(AIOpsConstants.DEPENDENCY_PARENT)));

                // for dependency level seven add parent in child like ********** -> 172.16.10.43 so in 172.16.10.43 add parent -1 == ********** if parent is ********** and if parent is 172.16.10.43 then ignore it to add parent
                if (AIOpsConstants.DependencyLevel.valueOfName(CommonUtil.getByteValue(event.getValue(AIOpsConstants.DEPENDENCY_LEVEL))) == AIOpsConstants.DependencyLevel.SIX)
                {
                    destination = event.getString(AIOpsConstants.DEPENDENCY_DESTINATION);

                    if (!event.getString(AIOpsConstants.DEPENDENCY_PARENT).equalsIgnoreCase(destination))
                    {
                        localDomainDependencies.computeIfAbsent(destination, value -> new HashMap<>());

                        localDomainConnections = localDomainDependencies.get(destination);

                        localDomainConnections.computeIfAbsent(level.getName(), value -> new TreeSet<>()).add(getHashCode(event.getString(AIOpsConstants.DEPENDENCY_PARENT)));
                    }
                }
            }

            // os add
            /*level = AIOpsConstants.DependencyLevel.FOUR;

            localDomainDependencies.get(key).computeIfAbsent(level.getName(),value -> new TreeSet<>());

            localDomainDependencies.get(key).get(level.getName()).add(getHashCode(source));*/
        }
        else
        {
            LOGGER.info(String.format("level FOUR does not contain %s ", event.getString(AIOpsConstants.DEPENDENCY_DEPENDENT_FIELD)));
        }
    }

    /**
     * Clears all dependencies at a specific level.
     * <p>
     * This method removes all dependencies at the specified level from the connections map.
     * If the level exists in the connections map, it is completely removed.
     * <p>
     * This method also sets the dirty flag to true if a level is removed to indicate that
     * the dependency data has been modified and should be persisted to disk.
     *
     * @param level       The dependency level to clear
     * @param connections The connection map to clear from
     */
    private void clear(Byte level, Map<Byte, Set<Integer>> connections)
    {
        if (connections != null && connections.get(level) != null)
        {
            connections.get(level).clear();

            dirty = true;
        }
    }

    /**
     * Removes all dependencies for a target.
     * <p>
     * This method removes all dependencies where the target is either a source or a destination.
     * It handles both direct dependencies (where the target is the source) and indirect
     * dependencies (where the target is a destination in another source's connections).
     * <p>
     * This method is typically called when an object is deleted from the system, to ensure
     * that all its dependencies are properly cleaned up.
     * <p>
     * This method also sets the dirty flag to true if any dependencies are removed to indicate
     * that the dependency data has been modified and should be persisted to disk.
     *
     * @param dependencies The dependency map to remove from
     * @param target       The target to remove all dependencies for
     */
    private void remove(Map<String, Map<Byte, Set<Integer>>> dependencies, String target)
    {
        if (dependencies.get(target) != null)
        {
            for (var entry : dependencies.get(target).entrySet())
            {
                for (var hashCode : entry.getValue())
                {
                    dependencies.remove(target + GlobalConstants.VALUE_SEPARATOR + objectsByHashCode.get(hashCode));

                    // ********** -> 172.16.10.43 we have and in 172.16.10.43 we have -1 = **********
                    // so when user delete ********** at that time we need to find that in 7 level if we have 172.16.10.43 and in 172.16.10.43 -> -1 level we have ********** then need to remove it
                    if (entry.getKey().equals(AIOpsConstants.DependencyLevel.SIX.getName()))
                    {
                        var key = objectsByHashCode.get(hashCode);

                        if (dependencies.get(key) != null && dependencies.get(key).containsKey(AIOpsConstants.DependencyLevel.MINUS_ONE.getName()))
                        {
                            var iterator = dependencies.get(key).get(AIOpsConstants.DependencyLevel.MINUS_ONE.getName()).iterator();

                            while (iterator.hasNext())
                            {
                                var parent = objectsByHashCode.get(iterator.next());

                                if (parent.equalsIgnoreCase(target))
                                {
                                    iterator.remove();
                                }
                            }

                            // if all levels are empty remove key from map
                            clear(key, dependencies);
                        }
                    }
                }
            }

            dirty = true;

            dependencies.remove(target);
        }
    }

    /**
     * Gets the hash code for a string value.
     * <p>
     * This method returns the hash code for a string value, which is used as an internal
     * representation of the value in the dependency maps. If the value is not already in
     * the objectHashCodes map, it is added with a new hash code.
     * <p>
     * Using hash codes instead of the original string values reduces memory usage and
     * improves performance for dependency operations.
     *
     * @param value The string value to get the hash code for
     * @return The hash code for the value
     */
    public int getHashCode(String value)
    {
        if (value != null)
        {
            objectHashCodes.computeIfAbsent(value, hash -> hashCode++);

            objectsByHashCode.put(objectHashCodes.get(value), value);

            return objectHashCodes.get(value);
        }
        else
        {
            return 0;
        }
    }
}
