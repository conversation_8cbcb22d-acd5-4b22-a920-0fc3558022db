{"entity": "Single Sign On", "collection": "single_sign_on", "version": "1.0", "author": "Sankalp", "props": [{"name": "saml.authentication.protocol", "title": "saml authentication protocol", "type": "string", "rules": ["required"]}, {"name": "saml.authentication.context", "title": "saml authentication Context", "type": "map", "rules": ["required"]}], "entries": [{"type": "inline", "records": [{"id": 10000000000001, "saml.authentication.context": {"saml.service.provider.entity.id": "Motadata-AIOps"}, "saml.authentication.protocol": "SAML2.0"}], "version": "1.0"}]}