#  Copyright (c) Motadata 2022. All rights reserved.

import argparse
import json
from base64 import b64encode, b64decode

from motadatasdk.constants import Constant
from motadatasdk.logger import Logger
from motadatasdk.util import Util


#"command" : "SELECT TOP (10) session_id,unsuccessful_logons, nt_user_name,nt_domain,memory_usage, cpu_time, reads, writes, host_name, program_name, login_name, status, CONVERT(varchar,login_time,21) login_time, total_elapsed_time from sys.dm_exec_sessions ORDER BY cpu_time DESC",

class MSSQLSessionByCPURunbookPlugin:

    def run(self, context):

        result = {Constant.STATUS: Constant.STATUS_FAIL, Constant.RESULT: {}}

        sessions = []

        try:

            for row in context.get(Constant.RESULT):

                session = {}

                if row.get("session_id") is not None:
                    session['mssql.session.id'] = Util.convert_to_int(row.get("session_id"))

                if row.get("memory_usage") is not None:
                    session['mssql.session.used.memory.bytes'] = (Util.convert_to_int(
                        row.get("memory_usage")) * 8) * 1024

                if row.get("cpu_time") is not None:
                    session['mssql.session.cpu.time.ms'] = Util.convert_to_int(row.get("cpu_time"))

                if row.get("unsuccessful_logons") is not None and row.get("unsuccessful_logons") != Constant.BLANK_STRING:

                    session['mssql.session.failed.logons'] = Util.convert_to_int(row.get("unsuccessful_logons"))

                else:

                    session['mssql.session.failed.logons'] = 0

                if row.get("reads") is not None:
                    session['mssql.session.reads'] = Util.convert_to_int(row.get("reads"))

                if row.get("writes") is not None:
                    session['mssql.session.writes'] = Util.convert_to_int(row.get("writes"))

                if row.get("host_name") is not None:

                    session['mssql.session.remote.client'] = Util.convert_to_string(row.get("host_name"))

                else:

                    session['mssql.session.remote.client'] = Constant.BLANK_STRING

                if row.get("nt_user_name") is not None:
                    session['mssql.session.domain.user'] = Util.convert_to_string(row.get("nt_user_name"))

                if row.get("nt_domain") is not None:
                    session['mssql.session.domain'] = Util.convert_to_string(row.get("nt_domain"))

                if row.get("program_name") != Constant.BLANK_STRING and \
                        len(Util.convert_to_string(row.get("program_name"))) > 0:

                    session['mssql.session.application'] = Util.convert_to_string(row.get("program_name"))

                else:

                    session['mssql.session.application'] = Constant.BLANK_STRING

                if row.get("login_name") is not None:
                    session['mssql.session.login.name'] = Util.convert_to_string(row.get("login_name"))

                if row.get("login_time") is not None:
                    session['mssql.session.login.time'] = Util.convert_to_string(row.get("login_time"))

                if row.get("status") is not None:
                    session['mssql.session.status'] = Util.convert_to_string(row.get("status"))

                if row.get("total_elapsed_time") is not None:
                    session['mssql.session.duration.sec'] = Util.convert_to_int(
                        Util.convert_to_int(row.get("total_elapsed_time")) / 1000)

                    session['mssql.session.duration'] = Util.convert_seconds_to_uptime(session['mssql.session.duration.sec'])

                if len(session) > 0:
                    sessions.append(session)

            if len(sessions) > 0:
                result[Constant.STATUS] = Constant.STATUS_SUCCEED

                result[Constant.RESULT]["mssql.session"] = sessions

        except Exception as exception:

            result[Constant.ERRORS] = [
                {Constant.ERROR: str(Logger.get_stack_trace()), Constant.MESSAGE: str(exception),
                 Constant.ERROR_CODE: Constant.ERROR_CODE_INTERNAL_ERROR}]

        return result

if __name__ == '__main__':
    parser = argparse.ArgumentParser()

    parser.add_argument('--context', type=str, help='Context')

    args, leftovers = parser.parse_known_args()

    print(b64encode(json.dumps(MSSQLSessionByCPURunbookPlugin().run(
        json.loads(b64decode(str(args.context)).decode()))).encode()).decode(), end=Constant.BLANK_STRING)