/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.misc;

import com.mindarray.*;
import com.mindarray.aiops.AIOpsConstants;
import com.mindarray.api.*;
import com.mindarray.config.ConfigConstants;
import com.mindarray.config.ConfigManager;
import com.mindarray.datastore.DatastoreConstants;
import com.mindarray.db.ConfigDBConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.job.*;
import com.mindarray.nms.*;
import com.mindarray.notification.Notification;
import com.mindarray.policy.MetricPolicyTriggerDurationCalculator;
import com.mindarray.store.*;
import com.mindarray.util.*;
import io.github.artsok.RepeatedIfExceptionsTest;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.eventbus.MessageConsumer;
import io.vertx.core.json.Json;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.junit5.Timeout;
import io.vertx.junit5.VertxExtension;
import io.vertx.junit5.VertxTestContext;
import org.apache.commons.io.FileUtils;
import org.apache.http.HttpStatus;
import org.joda.time.Duration;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.quartz.JobExecutionException;

import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.ArrayList;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.TestAPIConstants.*;
import static com.mindarray.api.APIConstants.*;
import static com.mindarray.api.MailServerConfiguration.*;
import static com.mindarray.api.RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_TYPE;
import static com.mindarray.api.RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID;
import static com.mindarray.api.SMSGatewayConfiguration.SMS_SERVER_GATEWAY_URL;
import static com.mindarray.config.ConfigConstants.*;
import static com.mindarray.db.ConfigDBConstants.*;
import static com.mindarray.eventbus.EventBusConstants.*;
import static com.mindarray.nms.NMSConstants.CLI_ENABLED;
import static com.mindarray.nms.NMSConstants.Type.SSH;
import static org.apache.http.HttpStatus.SC_BAD_REQUEST;
import static org.apache.http.HttpStatus.SC_OK;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;

@ExtendWith(VertxExtension.class)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@Timeout(100 * 1000)
@EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY$")
public class TestMiscellaneous
{
    private static final Logger LOGGER = new Logger(TestMiscellaneous.class, MOTADATA_API, "Test Miscellaneous");
    private static final JsonObject SMS_CONTEXT = new JsonObject().put(SMS_SERVER_GATEWAY_URL, "https://sampleserver.com?number=$$number$$&message=$$message$$")
            .put(TARGET, "7990311324")
            .put(MESSAGE, "Test message from motadata");
    private static JsonObject agentConfigs;
    private static JsonObject configs;
    private static long id;
    private static MessageConsumer<JsonObject> messageConsumer = null;

    private static long userId;

    @BeforeAll
    static void beforeAll(VertxTestContext testContext)
    {
        agentConfigs = new JsonObject().mergeIn(TestUtil.vertx().fileSystem().readFileBlocking(GlobalConstants.CURRENT_DIR + PATH_SEPARATOR + GlobalConstants.CONFIG_DIR + PATH_SEPARATOR + "agent.json").toJsonObject());

        configs = new JsonObject().mergeIn(TestUtil.vertx().fileSystem().readFileBlocking(GlobalConstants.CURRENT_DIR + PATH_SEPARATOR + GlobalConstants.CONFIG_DIR + PATH_SEPARATOR + "motadata.json").toJsonObject());

        var item = new JsonObject().put(User.USER_NAME, "testmisc")
                .put(User.USER_TYPE, User.USER_TYPE_SYSTEM)
                .put(User.USER_PASSWORD, new CipherUtil().encrypt("admin"))
                .put(User.USER_ROLE, CommonUtil.getLong(DEFAULT_ID))
                .put(FIELD_TYPE, ENTITY_TYPE_USER)
                .put(User.USER_PASSWORD_LAST_UPDATED_TIME, System.currentTimeMillis() - TimeUnit.DAYS.toMillis(29))
                .put(User.USER_PREFERENCES, new JsonObject());

        Bootstrap.configDBService().save(COLLECTION_USER, item, DEFAULT_USER, SYSTEM_REMOTE_ADDRESS, result ->
        {
            if (result.succeeded())
            {
                UserConfigStore.getStore().addItem(result.result()).onComplete(asyncResult -> testContext.completeNow());

                id = result.result();
            }
            else
            {
                LOGGER.error(result.cause());

                testContext.failNow(result.cause());
            }
        });
    }

    @AfterAll
    static void afterAll(VertxTestContext testContext)
    {
        LOGGER.info("Bootstrap Type: " + MotadataConfigUtil.getSystemBootstrapType().toLowerCase());

        if (messageConsumer != null)
        {
            messageConsumer.unregister(result ->
            {
                LOGGER.info("messageConsumer != null: ");

                Bootstrap.stop(false, null, false);

                testContext.completeNow();
            });
        }
        else
        {
            LOGGER.info("messageConsumer is null");

            Bootstrap.stop(false, null, false);

            testContext.completeNow();
        }
    }

    @BeforeEach
    void beforeEach(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running test case %s", testInfo.getTestMethod().get().getName()));

        testContext.completeNow();
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(1)
    void testExecuteMetricDatastoreRetentionJob(VertxTestContext testContext)
    {
        try
        {
            // reason for writing this test here is because in this test case we are removing all event file

            var eventDir = new File(CURRENT_DIR + PATH_SEPARATOR + EVENT_DIR);

            var files = eventDir.listFiles();

            if (files != null)
            {
                for (var file : files)
                {
                    if (file.getName().startsWith("datastore-write-0") && file.length() > 0)
                    {
                        var eventFile = new File(eventDir.getPath() + PATH_SEPARATOR + file.getName() + PATH_SEPARATOR + "event-files");

                        if (eventFile.exists() && eventFile.length() > 0)
                        {
                            var content = Files.readString(eventFile.toPath(), StandardCharsets.UTF_8);

                            if (!content.isEmpty())
                            {
                                var objects = new JsonArray(content);

                                var size = objects.size() / 2;

                                for (var index = 0; index < size - 1; index++)
                                {
                                    eventFile = new File(eventDir.getPath() + PATH_SEPARATOR + file.getName() + PATH_SEPARATOR + CommonUtil.getString(objects.getValue(index)) + ".dat");

                                    Assertions.assertTrue(eventFile.exists());
                                }

                                DatastoreConstants.runRetention(file.getName().replace("-", "."), CommonUtil.getLong(objects.getValue(size)), LOGGER);

                                testContext.awaitCompletion(10, TimeUnit.SECONDS);

                                for (var index = 0; index < size - 1; index++)
                                {
                                    eventFile = new File(eventDir.getPath() + PATH_SEPARATOR + file.getName() + PATH_SEPARATOR + CommonUtil.getString(objects.getValue(index)) + ".dat");

                                    Assertions.assertFalse(eventFile.exists());
                                }
                            }
                        }

                    }
                }
            }

            testContext.completeNow();

        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            testContext.failNow(exception);
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(2)
    void testExecuteEventDatastoreRetentionJob(VertxTestContext testContext)
    {
        try
        {
            // reason for writing this test here is because in this test case we are removing all event file

            var eventDir = new File(CURRENT_DIR + PATH_SEPARATOR + EVENT_DIR);

            var files = eventDir.listFiles();

            if (files != null)
            {
                for (var file : files)
                {
                    if (file.getName().startsWith("datastore-write-1") && file.length() > 0)
                    {
                        var eventFile = new File(eventDir.getPath() + PATH_SEPARATOR + file.getName() + PATH_SEPARATOR + "event-files");

                        if (eventFile.exists() && eventFile.length() > 0)
                        {
                            var content = Files.readString(eventFile.toPath(), StandardCharsets.UTF_8);

                            if (!content.isEmpty())
                            {
                                var objects = new JsonArray(content);

                                var size = objects.size() / 2;

                                for (var index = 0; index < size - 1; index++)
                                {
                                    eventFile = new File(eventDir.getPath() + PATH_SEPARATOR + file.getName() + PATH_SEPARATOR + CommonUtil.getString(objects.getValue(index)) + ".dat");

                                    Assertions.assertTrue(eventFile.exists());
                                }

                                DatastoreConstants.runRetention(file.getName().replace("-", "."), CommonUtil.getLong(objects.getValue(size)), LOGGER);

                                testContext.awaitCompletion(15, TimeUnit.SECONDS);

                                for (var index = 0; index < size - 1; index++)
                                {
                                    eventFile = new File(eventDir.getPath() + PATH_SEPARATOR + file.getName() + PATH_SEPARATOR + CommonUtil.getString(objects.getValue(index)) + ".dat");

                                    Assertions.assertFalse(eventFile.exists());
                                }
                            }
                        }

                    }
                }
            }

            testContext.completeNow();

        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            testContext.failNow(exception);
        }
    }

    @Test
    @Order(3)
    void testScheduleRetentionJob(VertxTestContext testContext) throws Exception
    {
        var caches = new JsonArray();

        var cacheFileDir = new File(CURRENT_DIR + PATH_SEPARATOR + CACHE_DIR);

        if (cacheFileDir.exists())
        {
            var files = cacheFileDir.listFiles();

            if (files != null)
            {
                for (var file : files)
                {
                    var tokens = file.getName().split(DASH_SEPARATOR);

                    if (CommonUtil.isNotNullOrEmpty(tokens[tokens.length - 2]) && !file.getName().contains(AIOpsConstants.CORRELATION_MAP))
                    {
                        tokens[tokens.length - 2] = "0" + CommonUtil.getString(CommonUtil.getInteger(tokens[tokens.length - 2]) - 1);

                        var name = new StringBuilder();

                        for (var token : tokens)
                        {
                            name.append(token).append(DASH_SEPARATOR);
                        }

                        name.replace(name.lastIndexOf(DASH_SEPARATOR), name.lastIndexOf(DASH_SEPARATOR) + 1, EMPTY_VALUE);

                        LOGGER.info(String.format("file is rename to : %s , %s ", file.getName(), name));

                        caches.add(name.toString());

                        file.renameTo(new File(CURRENT_DIR + PATH_SEPARATOR + CACHE_DIR + PATH_SEPARATOR + name.toString().trim()));
                    }
                }
            }
        }

        new DatastoreRetentionJob().execute(null);

        testContext.awaitCompletion(5, TimeUnit.SECONDS);

        for (var index = 0; index < caches.size(); index++)
        {
            Assertions.assertFalse(new File(CURRENT_DIR + PATH_SEPARATOR + CACHE_DIR + PATH_SEPARATOR + caches.getString(index).trim()).exists());
        }

        testContext.completeNow();
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(4)
    void testConfigurationBackup(VertxTestContext testContext, TestInfo testInfo) throws IOException
    {
        var promise = Promise.promise();

        var context = DataRetentionPolicyConfigStore.getStore().getItem();

        context.getJsonObject(DataRetentionPolicy.DATA_RETENTION_POLICY_CONTEXT).getJsonObject("CONFIG").put(VERSION, 3);

        TestAPIUtil.put(TestAPIConstants.DATA_RETENTION_API_ENDPOINT + DEFAULT_ID, context, response ->
        {
            if (response.succeeded())
            {
                LOGGER.info(String.format("%s with response %s", testInfo.getTestMethod().get().getName(), response.result().bodyAsJsonObject().encode()));

                var profile = CredentialProfileConfigStore.getStore().getItemByValue(CredentialProfile.CREDENTIAL_PROFILE_NAME, "***********-NCM-Credential-profile");

                var item = new JsonObject().put(CredentialProfile.CREDENTIAL_PROFILE_NAME, "***********-NCM-Credential-profile")
                        .put(CredentialProfile.CREDENTIAL_PROFILE_PROTOCOL, SSH.getName())
                        .put(CredentialProfile.CREDENTIAL_PROFILE_CONTEXT, new JsonObject()
                                .put(USERNAME, profile.getJsonObject(CredentialProfile.CREDENTIAL_PROFILE_CONTEXT).getString(USERNAME))
                                .put(PASSWORD, profile.getJsonObject(CredentialProfile.CREDENTIAL_PROFILE_CONTEXT).getString(PASSWORD))
                                .put(PROMPT, profile.getJsonObject(CredentialProfile.CREDENTIAL_PROFILE_CONTEXT).getString(PROMPT))
                                .put(CLI_ENABLED, profile.getJsonObject(CredentialProfile.CREDENTIAL_PROFILE_CONTEXT).getString(CLI_ENABLED))
                                .put(FILE_TRANSFER_PROTOCOL, StorageProfile.StorageProtocol.TFTP.getName())
                                .put(ENABLE_PROMPT, profile.getJsonObject(CredentialProfile.CREDENTIAL_PROFILE_CONTEXT).getString("enable.prompt"))
                                .put(ENABLE_PASSWORD, profile.getJsonObject(CredentialProfile.CREDENTIAL_PROFILE_CONTEXT).getString("enable.password")));

                Bootstrap.configDBService().update(COLLECTION_CREDENTIAL_PROFILE, new JsonObject().put(FIELD_NAME, ID).put(VALUE, profile.getLong(ID)), item, DEFAULT_USER, SYSTEM_REMOTE_ADDRESS, result ->
                        CredentialProfileConfigStore.getStore().updateItem(profile.getLong(ID)).onComplete(asyncResult -> promise.complete()));
            }
            else
            {
                LOGGER.info(String.format("%s failed with reason %s", testInfo.getTestMethod().get().getName(), response.cause().getMessage()));

                testContext.failNow(response.cause());
            }
        });

        var futures = new ArrayList<Future<Void>>();

        promise.future().onComplete(asyncResult ->
        {
            try
            {
                var id = ConfigurationConfigStore.getStore().getItemByValue("object.name", "ospf1.ospf1.com") != null ? ConfigurationConfigStore.getStore().getItemByValue("object.name", "ospf1.ospf1.com").getLong(GlobalConstants.ID) : ConfigurationConfigStore.getStore().getItemByValue("object.name", "***********").getLong(GlobalConstants.ID);

                for (var count = 0; count < 5; count++)
                {
                    var future = Promise.<Void>promise();

                    futures.add(future.future());

                    FileUtils.writeStringToFile(new File(ConfigConstants.CONFIG_MANAGEMENT_DEFAULT_DIR + PATH_SEPARATOR + "B_10000_R_1695734109.cfg"), String.format("backup file content for retention %d", count), StandardCharsets.UTF_8);

                    FileUtils.writeStringToFile(new File(ConfigConstants.CONFIG_MANAGEMENT_DEFAULT_DIR + PATH_SEPARATOR + "B_10000_S_1695734111.cfg"), String.format("backup startup file content for retention %d", count), StandardCharsets.UTF_8);

                    testContext.awaitCompletion(5, TimeUnit.SECONDS);

                    TestAPIUtil.post(TestAPIConstants.CONFIGURATION_API_ENDPOINT + PATH_SEPARATOR + "execute-operation", new JsonObject().put(CONFIG_OPERATION, ConfigConstants.ConfigOperation.BACKUP.getName()).put("objects", new JsonArray().add(id)), testContext.succeeding(response ->
                            testContext.verify(() ->
                            {
                                LOGGER.debug(response.bodyAsJsonObject());

                                var result = response.bodyAsJsonObject();

                                assertEquals(STATUS_SUCCEED, result.getString(STATUS));

                                assertEquals(HttpStatus.SC_OK, result.getInteger(APIConstants.RESPONSE_CODE));

                                assertEquals(String.format(InfoMessageConstants.CONFIG_REQUEST_QUEUED, ConfigConstants.ConfigOperation.BACKUP.getName()), result.getString(MESSAGE));

                                future.complete();
                            })));
                }
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);

                testContext.failNow(exception);
            }
        });

        Future.join(futures).onComplete(asyncResult ->
                testContext.completeNow());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(5)
    void testConfigBackupRetention(VertxTestContext testContext, TestInfo testInfo) throws InterruptedException
    {
        testContext.awaitCompletion(10, TimeUnit.SECONDS);

        TestUtil.vertx().eventBus().send(EVENT_CONFIG_BACKUP_CLEANUP, new JsonObject().put(VERSION, 1));

        TestUtil.vertx().setTimer(5 * 1000, timer ->
        {
            var item = ConfigurationConfigStore.getStore().getItemByValue("object.name", "ospf1.ospf1.com") != null ? ConfigurationConfigStore.getStore().getItemByValue("object.name", "ospf1.ospf1.com") : ConfigurationConfigStore.getStore().getItemByValue("object.name", "***********");

            LOGGER.info(String.format("%s item : %s", testInfo.getTestMethod().get().getName(), item));

            assertEquals(item.getInteger(Configuration.CONFIG_BACKUP_FILE_RUNNING_CURRENT_VERSION), item.getInteger(Configuration.CONFIG_BACKUP_FILE_RUNNING_MIN_VERSION));

            testContext.completeNow();
        });
    }

    @Test
    @Order(6)
    void testGetIndexableColumns(VertxTestContext testContext)
    {
        TestAPIUtil.get(MISC_INDEXABLE_API_ENDPOINT + "?filter=" + new JsonObject().put(PLUGIN_ID, new JsonArray().add(500000)), response ->
        {
            if (response.succeeded())
            {
                var result = response.result().bodyAsJsonObject();

                LOGGER.info(String.format("response of indexable columns : %s ", result.encode()));

                assertEquals(SC_OK, result.getInteger(RESPONSE_CODE));

                assertEquals(STATUS_SUCCEED, result.getString(STATUS));

                testContext.completeNow();
            }
            else
            {
                LOGGER.error(response.cause());

                testContext.failNow(response.cause());
            }
        });
    }

    @Test
    @Order(7)
    void testChangeLogLevelAndGet(VertxTestContext testContext) throws Exception
    {
        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
        {

            if (message.body().containsKey(EVENT_TYPE) && message.body().getString(EVENT_TYPE).equalsIgnoreCase(UI_ACTION_LOGGER_LEVEL_GET) && message.body().getBinary(EVENT_CONTEXT) != null)
            {
                var context = CodecUtil.toJSONObject(message.body().getBinary(EventBusConstants.EVENT_CONTEXT));

                LOGGER.debug("Received context from log level change and get : " + context.encode());

                Assertions.assertEquals(HttpStatus.SC_OK, context.getInteger(RESPONSE_CODE));

                Assertions.assertEquals(STATUS_SUCCEED, context.getString(STATUS));

                Assertions.assertEquals(2, context.getInteger(SYSTEM_LOG_LEVEL));

                messageConsumer.unregister(result -> testContext.completeNow());
            }
        });

        LOGGER.info(String.format("current log level : %s ", CommonUtil.getLogLevel()));

        TestUtil.vertx().eventBus().send(UI_ACTION_LOGGER_LEVEL_CHANGE, new JsonObject().put(APIConstants.SESSION_ID, TestUtil.getSessionId()).put(USERNAME, "admin").put(SYSTEM_LOG_LEVEL, 2).put("log.level.reset.timer.seconds", 10).put(REMOTE_EVENT_PROCESSOR_UUID, Bootstrap.getRegistrationId()).put(REMOTE_EVENT_PROCESSOR_TYPE, "APP"));

        testContext.awaitCompletion(5, TimeUnit.SECONDS);

        TestUtil.vertx().eventBus().send(UI_ACTION_LOGGER_LEVEL_GET, new JsonObject().put(APIConstants.SESSION_ID, TestUtil.getSessionId()));

    }

    @Test
    @Order(8)
    void testFailedSMSGateway(VertxTestContext testContext)
    {
        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
        {

            if (message.body().containsKey(EVENT_TYPE) && message.body().getString(EVENT_TYPE).equalsIgnoreCase(UI_ACTION_SMS_GATEWAY_CONFIGURATION_TEST) && message.body().getBinary(EVENT_CONTEXT) != null)
            {
                var context = CodecUtil.toJSONObject(message.body().getBinary(EventBusConstants.EVENT_CONTEXT));

                LOGGER.debug("Received context : " + context);

                Assertions.assertFalse(context.getJsonArray(ERRORS).isEmpty());

                Assertions.assertEquals(STATUS_FAIL, context.getString(STATUS));

                Assertions.assertTrue(context.getString(MESSAGE).contains("SMS Gateway Test Failed"));

                messageConsumer.unregister(result -> testContext.completeNow());
            }

        });

        TestUtil.vertx().eventBus().send(UI_ACTION_SMS_GATEWAY_CONFIGURATION_TEST, SMS_CONTEXT.copy().put(APIConstants.SESSION_ID, TestUtil.getSessionId()).put(User.USER_NAME, "admin"));

    }

    @Test
    @Order(9)
    void testTestMailServerProxyEnabled(VertxTestContext testContext)
    {
        var uuid = UUID.randomUUID().toString();

        try
        {
            messageConsumer = Bootstrap.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
            {
                try
                {
                    var eventContext = CodecUtil.toJSONObject(message.body().getBinary(EventBusConstants.EVENT_CONTEXT));

                    if (message.body().getString(EVENT_TYPE) != null && message.body().getString(EVENT_TYPE).equals(UI_ACTION_MAIL_SERVER_CONFIGURATION_TEST) &&
                            !eventContext.isEmpty() && eventContext.containsKey(UI_EVENT_UUID) && eventContext.getString(UI_EVENT_UUID).equalsIgnoreCase(uuid)
                            && eventContext.getString(STATUS) != null && eventContext.getString(MESSAGE) != null && message.body().containsKey(EVENT_TYPE))
                    {
                        testContext.verify(() ->
                        {
                            assertEquals(STATUS_SUCCEED, eventContext.getString(STATUS));

                            assertEquals(ErrorCodes.ERROR_CODE_SUCCESS, eventContext.getString(ERROR_CODE));

                            Assertions.assertTrue(eventContext.getString(MESSAGE).contains(InfoMessageConstants.MAIL_SERVER_TEST_SUCCEEDED));

                            messageConsumer.unregister(result -> testContext.completeNow());
                        });
                    }
                }
                catch (Exception exception)
                {
                    messageConsumer.unregister();

                    testContext.failNow(exception);
                }
            });
        }
        catch (Exception exception)
        {
            messageConsumer.unregister();

            testContext.failNow(exception);
        }

        Bootstrap.vertx().eventBus().send(EVENT_SERVER, new JsonObject().put(EVENT_TYPE, UI_ACTION_MAIL_SERVER_CONFIGURATION_TEST).put(SESSION_ID, TestUtil.getSessionId())
                .put(EVENT_CONTEXT, new JsonObject()
                        .put(MAIL_SERVER_HOST, "smtp.office365.com")
                        .put(MAIL_SERVER_PORT, 587)
                        .put(MAIL_SERVER_CREDENTIAL_PROFILE, CredentialProfile.DEFAULT_EMAIL_CREDENTIAL_PROFILE)
                        .put(MAIL_SERVER_PROTOCOL, MAIL_SERVER_PROTOCOL_TLS)
                        .put(MAIL_SERVER_AUTH_STATUS, YES)
                        .put(MAIL_SERVER_USERNAME, "<EMAIL>")
                        .put(Notification.EMAIL_NOTIFICATION_RECIPIENTS, "<EMAIL>")
                        .put(ProxyServer.PROXY_ENABLED, YES)
                        .put(MAIL_SERVER_PASSWORD, "Mind#@45Date!12").put(UI_EVENT_UUID, uuid).put(SESSION_ID, TestUtil.getSessionId())));


    }

    @Test
    @Order(10)
    @Disabled
    void testTestSMSGatewayProxyEnabled(VertxTestContext testContext)
    {
        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
        {

            if (message.body().containsKey(EVENT_TYPE) && message.body().getString(EVENT_TYPE).equalsIgnoreCase(UI_ACTION_SMS_GATEWAY_CONFIGURATION_TEST) && message.body().getBinary(EVENT_CONTEXT) != null)
            {
                var context = CodecUtil.toJSONObject(message.body().getBinary(EventBusConstants.EVENT_CONTEXT));

                LOGGER.debug("Received context : " + context);

                Assertions.assertTrue(context.getJsonArray(ERRORS).isEmpty());

                Assertions.assertEquals(STATUS_SUCCEED, context.getString(STATUS));

                messageConsumer.unregister(result -> testContext.completeNow());
            }

        });

        TestUtil.vertx().eventBus().send(UI_ACTION_SMS_GATEWAY_CONFIGURATION_TEST, SMS_CONTEXT.copy().put(SMS_SERVER_GATEWAY_URL, "http://api.textlocal.in/send/?username=<EMAIL>&hash=b3fb8f0b0e5d2c0ca467faf2a4baac0fc794acb7e4f7b3873e874efd3d4134c0&sender=TXTLCL&numbers=$$number$$&message=$$message$$")
                .put(ProxyServer.PROXY_ENABLED, YES).put(APIConstants.SESSION_ID, TestUtil.getSessionId()).put(User.USER_NAME, "admin"));

    }

    @Test
    @Order(11)
    void testUpdateMailServerProxyEnabled(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = new JsonObject().put(ProxyServer.PROXY_ENABLED, YES).put(MAIL_SERVER_CREDENTIAL_PROFILE, CredentialProfile.DEFAULT_EMAIL_CREDENTIAL_PROFILE);

        TestAPIUtil.put(EMAIL_CONFIG_API_ENDPOINT + DEFAULT_ID, context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertUpdateEntityTestResult(MailServerConfigStore.getStore(), context, response.bodyAsJsonObject(),
                                    String.format(InfoMessageConstants.ENTITY_UPDATED, "Mail Server Configuration"), LOGGER, testInfo.getTestMethod().get().getName());

                            testContext.completeNow();
                        })));
    }

    @Test
    @Order(12)
    void testUpdateSMSGatewayConfigurationProxyEnabled(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = new JsonObject()
                .put(SMS_SERVER_GATEWAY_URL, "http://api.textlocal.in/send/?username=<EMAIL>&hash=qSi5JdhO4tc-xQFfwCUTRbW82FbTQ0nSZtOecVDbWCd&sender=TXTLCL&numbers=$$number$$&message=$$message$$")
                .put(ProxyServer.PROXY_ENABLED, YES);

        TestAPIUtil.put(SMS_GATEWAY_CONFIGURATION_API_ENDPOINT + GlobalConstants.DEFAULT_ID, context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertUpdateEntityTestResult(SMSGatewayConfigStore.getStore(), context, response.bodyAsJsonObject(),
                                    String.format(InfoMessageConstants.ENTITY_UPDATED, "SMS Gateway Configuration"), LOGGER, testInfo.getTestMethod().get().getName());

                            testContext.completeNow();

                        })));

    }

    @Test
    @Order(13)
    void testUIActionLogonSession(VertxTestContext testContext)
    {
        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
        {

            if (message.body().containsKey(EVENT_TYPE) && message.body().getString(EVENT_TYPE).equalsIgnoreCase(UI_ACTION_LOGON_SESSION_POPULATE) && message.body().getBinary(EVENT_CONTEXT) != null)
            {
                var context = CodecUtil.toJSONObject(message.body().getBinary(EventBusConstants.EVENT_CONTEXT));

                Assertions.assertFalse(context.getJsonArray(RESULT).getJsonObject(0).isEmpty());

                messageConsumer.unregister(result -> testContext.completeNow());
            }
        });

        TestUtil.vertx().eventBus().send(UI_ACTION_USER_NAVIGATE, new JsonObject().put(SESSION_ID, TestUtil.getSessionId()).put(EVENT_CONTEXT, new JsonObject().put("os.name", OS_NAME).put(User.USER_ID, UserConfigStore.getStore().getItems().getJsonObject(0).getLong(ID))));

        TestUtil.vertx().eventBus().send(UI_ACTION_LOGON_SESSION_POPULATE, new JsonObject().put(SESSION_ID, TestUtil.getSessionId()));
    }

    @Test
    @Order(14)
    void testCommonUtilGetterMethods(VertxTestContext testContext)
    {
        if (!agentConfigs.isEmpty() && !configs.isEmpty())
        {
            Assertions.assertTrue(true);

            Assertions.assertTrue(AgentConfigUtil.getAgentLogRetentionDays() > 0);

            Assertions.assertTrue(AgentConfigUtil.getAgentCacheFlushTimerSeconds() >= 10000);

            Assertions.assertEquals(AgentConfigUtil.getAgentCacheFileMaxSizeBytes(), agentConfigs.containsKey("cache.file.max.size.mb") ? agentConfigs.getLong("cache.file.max.size.mb") : 104857600L);

            Assertions.assertTrue(AgentConfigUtil.getAgentHealthInspectionTimerSeconds() >= 60);

            Assertions.assertTrue(AgentConfigUtil.getAgentHealthWindowCount() >= 1);

            Assertions.assertTrue(AgentConfigUtil.getAgentHTTPServerPort() == 443 || AgentConfigUtil.getAgentHTTPServerPort() == 8443);

            Assertions.assertEquals(MotadataConfigUtil.getFlowPort(), configs.containsKey("flow.listener.udp.port") ? configs.getInteger("flow.listener.udp.port") : 3306);

            Assertions.assertEquals(MotadataConfigUtil.getLogProcessorPort(), configs.containsKey("log.processor.port") ? configs.getInteger("log.processor.port") : 9443);

            Assertions.assertEquals(MotadataConfigUtil.getAIRequestPort(), configs.containsKey("ai.request.port") ? configs.getInteger("ai.request.port") : 9451);

            Assertions.assertTrue(MotadataConfigUtil.getUserNotificationEntries() >= 100);

            Assertions.assertEquals(MotadataConfigUtil.getDBServerHost(), configs.containsKey("db.host") ? configs.getString("db.host") : "localhost");

            Assertions.assertTrue(MotadataConfigUtil.getStreamingSessionTimeoutMillis() >= 5000);

            Assertions.assertTrue(MotadataConfigUtil.getGeoDBRequestTimeoutSeconds() >= 60);

            Assertions.assertTrue(MotadataConfigUtil.getEventHistoryQueryQueueSize() >= 15);

            Assertions.assertTrue(MotadataConfigUtil.getDatastoreRequestBatchSize() >= 100);

            configs.put("https", YES).put("log.level.reset.timer.seconds", 5).put("flow.log.level", 2).remove("http.server.port");

            try
            {
                MotadataConfigUtil.loadConfigs(configs);

                Assertions.assertEquals(MotadataConfigUtil.getSystemBootstrapType(), BootstrapType.APP.name());
            }
            catch (Exception exception)
            {
                testContext.failNow(exception.getCause());
            }

            Assertions.assertTrue(MotadataConfigUtil.httpsEnabled());

            Assertions.assertEquals(200L, CommonUtil.getLong(200L, 100L));

            Assertions.assertEquals(5, MotadataConfigUtil.getLogLevelResetTimerSeconds());

            Assertions.assertEquals(2, MotadataConfigUtil.getFlowLogLevel());

            testContext.completeNow();
        }
    }

    @ParameterizedTest
    @ValueSource(ints = {0, 1, 2, 3, 4})
    @Order(15)
    void testCommonUtilDeploymentTypes(int deploymentType, VertxTestContext testContext)
    {
        var PATH = CURRENT_DIR + PATH_SEPARATOR + CONFIG_DIR + PATH_SEPARATOR + "motadata.json";

        switch (deploymentType)
        {
            case 0 ->
            {

                configs.put("deployment.type", deploymentType);

                try
                {
                    MotadataConfigUtil.loadConfigs(configs);

                    Assertions.assertEquals(MotadataConfigUtil.getSystemBootstrapType(), BootstrapType.APP.name());
                }
                catch (Exception exception)
                {
                    testContext.failNow(exception.getCause());
                }

                Assertions.assertEquals(1, MotadataConfigUtil.getWorkers());

                Assertions.assertEquals(8, MotadataConfigUtil.getHelperWorkers());

                Assertions.assertEquals(2, MotadataConfigUtil.getMetricPolicyWorkers());

                Assertions.assertEquals(2, MotadataConfigUtil.getEventPolicyWorkers());

                Assertions.assertEquals(1, MotadataConfigUtil.getEventPolicyQualifierWorkers());

                Assertions.assertEquals(2, MotadataConfigUtil.getMetricEnricherInstances());

                Assertions.assertEquals(1, MotadataConfigUtil.getRouterInstances());

                Assertions.assertEquals(2, MotadataConfigUtil.getMetricDatastoreWorkerInstances());

                Assertions.assertEquals(2, MotadataConfigUtil.getEventDatastoreWorkerInstances());

                Assertions.assertEquals(1, MotadataConfigUtil.getDependencyLocalDomainInstances());

                Assertions.assertEquals(2, MotadataConfigUtil.getVisualizationManagerInstances());

                Assertions.assertEquals(2, MotadataConfigUtil.getReportManagerInstances());
            }
            case 1 ->
            {

                configs.put("deployment.type", deploymentType);

                try
                {
                    MotadataConfigUtil.loadConfigs(configs);

                    Assertions.assertEquals(MotadataConfigUtil.getSystemBootstrapType(), BootstrapType.APP.name());
                }
                catch (Exception exception)
                {
                    testContext.failNow(exception.getCause());
                }

                Assertions.assertEquals(2, MotadataConfigUtil.getWorkers());

                Assertions.assertEquals(16, MotadataConfigUtil.getHelperWorkers());

                Assertions.assertEquals(4, MotadataConfigUtil.getMetricPolicyWorkers());

                Assertions.assertEquals(4, MotadataConfigUtil.getEventPolicyWorkers());

                Assertions.assertEquals(2, MotadataConfigUtil.getEventPolicyQualifierWorkers());

                Assertions.assertEquals(4, MotadataConfigUtil.getMetricEnricherInstances());

                Assertions.assertEquals(2, MotadataConfigUtil.getRouterInstances());

                Assertions.assertEquals(4, MotadataConfigUtil.getMetricDatastoreWorkerInstances());

                Assertions.assertEquals(4, MotadataConfigUtil.getEventDatastoreWorkerInstances());

                Assertions.assertEquals(2, MotadataConfigUtil.getDependencyLocalDomainInstances());

                Assertions.assertEquals(4, MotadataConfigUtil.getVisualizationManagerInstances());

                Assertions.assertEquals(4, MotadataConfigUtil.getReportManagerInstances());

            }
            case 2 ->
            {

                configs.put("deployment.type", deploymentType);

                TestUtil.vertx().fileSystem().writeFileBlocking(PATH, Buffer.buffer(Json.encodePrettily(configs)));

                try
                {
                    MotadataConfigUtil.loadConfigs(new JsonObject(Files.readString(Path.of(PATH), StandardCharsets.UTF_8)));

                    Assertions.assertEquals(MotadataConfigUtil.getSystemBootstrapType(), BootstrapType.APP.name());
                }
                catch (Exception exception)
                {
                    testContext.failNow(exception.getCause());
                }

                Assertions.assertEquals(3, MotadataConfigUtil.getWorkers());

                Assertions.assertEquals(32, MotadataConfigUtil.getHelperWorkers());

                Assertions.assertEquals(6, MotadataConfigUtil.getMetricPolicyWorkers());

                Assertions.assertEquals(8, MotadataConfigUtil.getEventPolicyWorkers());

                Assertions.assertEquals(4, MotadataConfigUtil.getEventPolicyQualifierWorkers());

                Assertions.assertEquals(6, MotadataConfigUtil.getMetricEnricherInstances());

                Assertions.assertEquals(4, MotadataConfigUtil.getRouterInstances());

                Assertions.assertEquals(6, MotadataConfigUtil.getMetricDatastoreWorkerInstances());

                Assertions.assertEquals(6, MotadataConfigUtil.getEventDatastoreWorkerInstances());

                Assertions.assertEquals(4, MotadataConfigUtil.getDependencyLocalDomainInstances());

                Assertions.assertEquals(6, MotadataConfigUtil.getVisualizationManagerInstances());

                Assertions.assertEquals(8, MotadataConfigUtil.getReportManagerInstances());
            }
            case 3 ->
            {

                configs.put("deployment.type", deploymentType);

                TestUtil.vertx().fileSystem().writeFileBlocking(PATH, Buffer.buffer(Json.encodePrettily(configs)));

                try
                {
                    MotadataConfigUtil.loadConfigs(new JsonObject(Files.readString(Path.of(PATH), StandardCharsets.UTF_8)));

                    Assertions.assertEquals(MotadataConfigUtil.getSystemBootstrapType(), BootstrapType.APP.name());
                }
                catch (Exception exception)
                {
                    testContext.failNow(exception.getCause());
                }

                Assertions.assertEquals(5, MotadataConfigUtil.getWorkers());

                Assertions.assertEquals(64, MotadataConfigUtil.getHelperWorkers());

                Assertions.assertEquals(8, MotadataConfigUtil.getMetricPolicyWorkers());

                Assertions.assertEquals(12, MotadataConfigUtil.getEventPolicyWorkers());

                Assertions.assertEquals(8, MotadataConfigUtil.getEventPolicyQualifierWorkers());

                Assertions.assertEquals(8, MotadataConfigUtil.getMetricEnricherInstances());

                Assertions.assertEquals(8, MotadataConfigUtil.getRouterInstances());

                Assertions.assertEquals(8, MotadataConfigUtil.getMetricDatastoreWorkerInstances());

                Assertions.assertEquals(8, MotadataConfigUtil.getEventDatastoreWorkerInstances());

                Assertions.assertEquals(8, MotadataConfigUtil.getDependencyLocalDomainInstances());

                Assertions.assertEquals(10, MotadataConfigUtil.getVisualizationManagerInstances());

                Assertions.assertEquals(16, MotadataConfigUtil.getReportManagerInstances());
            }
            case 4 ->
            {

                configs.put("deployment.type", deploymentType);

                TestUtil.vertx().fileSystem().writeFileBlocking(PATH, Buffer.buffer(Json.encodePrettily(configs)));

                try
                {
                    MotadataConfigUtil.loadConfigs(new JsonObject(Files.readString(Path.of(PATH), StandardCharsets.UTF_8)));

                    Assertions.assertEquals(MotadataConfigUtil.getSystemBootstrapType(), BootstrapType.APP.name());
                }
                catch (Exception ignored)
                {
                }

                Assertions.assertEquals(1, MotadataConfigUtil.getWorkers());

                Assertions.assertEquals(8, MotadataConfigUtil.getHelperWorkers());

                Assertions.assertEquals(2, MotadataConfigUtil.getMetricPolicyWorkers());

                Assertions.assertEquals(2, MotadataConfigUtil.getEventPolicyWorkers());

                Assertions.assertEquals(1, MotadataConfigUtil.getEventPolicyQualifierWorkers());

                Assertions.assertEquals(2, MotadataConfigUtil.getMetricEnricherInstances());

                Assertions.assertEquals(1, MotadataConfigUtil.getRouterInstances());

                Assertions.assertEquals(2, MotadataConfigUtil.getMetricDatastoreWorkerInstances());

                Assertions.assertEquals(2, MotadataConfigUtil.getEventDatastoreWorkerInstances());

                Assertions.assertEquals(1, MotadataConfigUtil.getDependencyLocalDomainInstances());

                Assertions.assertEquals(2, MotadataConfigUtil.getVisualizationManagerInstances());

                Assertions.assertEquals(2, MotadataConfigUtil.getReportManagerInstances());
            }
        }

        testContext.completeNow();
    }

    @Test
    @Order(16)
    void testExtractErrors(VertxTestContext testContext)
    {
        var errors = new JsonArray(new ArrayList<JsonObject>(1)).add(new JsonObject().put(ERROR_CODE, ErrorCodes.ERROR_CODE_INVALID_PORT).put(MESSAGE, String.format(ErrorMessageConstants.PORT_CONNECTION_FAILED, "8443")).put(ERROR, String.format(ErrorMessageConstants.PORT_CONNECTION_FAILED, "8443")));

        var error = ErrorMessageConstants.extractError(errors);

        Assertions.assertEquals(String.format(ErrorMessageConstants.PORT_CONNECTION_FAILED + GlobalConstants.NEW_LINE, "8443"), error);

        testContext.completeNow();

    }

    @Test
    @Order(17)
    void testDNSCacheFlushJob(VertxTestContext testContext)
    {
        new DNSCacheFlushJob().execute(null);

        /* As of now completing testcase without assertion because DNS functionality is not fully functional
        (In future there must be an assertion that assures empty records in DNSCacheStore)
        */

        testContext.completeNow();
    }

    @Test
    @Order(18)
    void testGenerateTrialLicense(VertxTestContext testContext)
    {
        File existing = new File(LicenseUtil.LICENSE_FILE);

        File auxiliary = new File(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + "old-license.lic");

        try
        {
            if (existing.renameTo(auxiliary))
            {
                LicenseUtil.validate(true, null).onComplete(result ->
                {

                    if (result.succeeded())
                    {
                        try
                        {
                            LOGGER.debug(String.format("license details %s ", LicenseUtil.getLicenseDetails().encode()));

                            Assertions.assertEquals(100, LicenseUtil.getLicenseDetails().getInteger("licensed.monitors"));

                            Assertions.assertTrue(auxiliary.renameTo(existing));

                            testContext.awaitCompletion(3, TimeUnit.SECONDS);

                            LOGGER.debug(String.format("license details %s ", LicenseUtil.getLicenseDetails().encode()));

                            Assertions.assertEquals(2000, LicenseUtil.getLicenseDetails().getInteger("licensed.monitors"));
                        }
                        catch (Exception exception)
                        {
                            LOGGER.error(exception);

                            testContext.failNow(exception);
                        }

                        testContext.completeNow();
                    }
                    else
                    {
                        testContext.failNow(result.cause());
                    }
                });
            }
            else
            {
                testContext.failNow("Cannot rename license file");
            }
        }

        catch (Exception exception)
        {
            testContext.failNow(exception.getCause());
        }

        finally
        {
            if (auxiliary.exists())
            {
                auxiliary.renameTo(existing);
            }
        }
    }

    @Test
    @Order(19)
    void testLicenceExpiry(VertxTestContext testContext) throws JobExecutionException, InterruptedException
    {
        LicenseUtil.LICENSE_TYPE.set(LicenseUtil.LicenseType.SUBSCRIPTION);

        LicenseUtil.LICENSE_EXPIRY_DATE.set(DateTimeUtil.currentMilliSeconds());

        var user = UserConfigStore.getStore().getItem(DEFAULT_ID);

        user.put(User.USER_EMAIL, "<EMAIL>");

        user.getJsonObject(User.USER_PREFERENCES).put(User.USER_PREFERENCE_DATE_TIME_FORMAT, "DD/MMM/YYYY");

        UserConfigStore.getStore().addItem(user.getLong(ID), user);

        LOGGER.trace("user : " + UserConfigStore.getStore().getItem(id).encode());

        LOGGER.trace("remaining days : " + Duration.millis((LicenseUtil.LICENSE_EXPIRY_DATE.get() - DateTimeUtil.currentMilliSeconds())).toStandardDays().getDays());

        ActiveUserCacheStore.getStore().updateItem("demo-uuid", new JsonObject().put(User.USER_ID, id));

        LOGGER.trace("active users : " + ActiveUserCacheStore.getStore().getItems());

        Assertions.assertTrue(DateTimeUtil.roundOffSeconds(System.currentTimeMillis(), 7200) > 0L);

        Assertions.assertTrue(DateTimeUtil.roundOffSeconds(System.currentTimeMillis(), 2000) > 0L);

        testContext.completeNow();
    }

    @Test
    @Order(20)
    void testExecuteNotificationJob(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info("running test case : " + testInfo.getTestMethod().get().getName());

        try
        {
            var passwordEvent = new AtomicBoolean();

            var licenseEvent = new AtomicBoolean();

            TestUtil.vertx().eventBus().<JsonObject>localConsumer(EVENT_USER + "demo-uuid", message ->
            {
                var event = CodecUtil.toJSONObject(message.body().getBinary(EventBusConstants.EVENT_CONTEXT));

                try
                {
                    if (message.body().containsKey(EVENT_TYPE) && message.body().getString(EVENT_TYPE).equalsIgnoreCase(UI_NOTIFICATION_PASSWORD_EXPIRE))
                    {
                        LOGGER.trace("received password expire event : " + message.body().encode());

                        Assertions.assertTrue(event.containsKey("remaining.days"));

                        passwordEvent.set(true);
                    }
                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);

                    testContext.failNow(exception);
                }
            });

            TestUtil.vertx().eventBus().<JsonObject>localConsumer(EVENT_USER + "demo-uuid", message ->
            {
                var event = CodecUtil.toJSONObject(message.body().getBinary(EventBusConstants.EVENT_CONTEXT));

                try
                {
                    if (message.body().containsKey(EVENT_TYPE) && message.body().getString(EVENT_TYPE).equalsIgnoreCase(UI_NOTIFICATION_LICENSE_EXPIRE))
                    {
                        LOGGER.trace("received license expire event : " + message.body().encode());

                        Assertions.assertTrue(event.containsKey("remaining.days"));

                        licenseEvent.set(true);
                    }
                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);

                    testContext.failNow(exception);
                }
            });

            TestUtil.vertx().setTimer(TimeUnit.SECONDS.toMillis(30), timer ->
            {
                if (passwordEvent.get() && licenseEvent.get())
                {
                    testContext.completeNow();
                }
                else
                {
                    testContext.failNow("notification job not executed successfully!!");
                }
            });

            new SystemNotificationJob().execute(null);
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            testContext.failNow(exception);
        }
    }

    @Test
    @Order(21)
    void testExecuteLicenseJob(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info("running test case : " + testInfo.getTestMethod().get().getName());

        try
        {
            new LicenseJob().execute(null);

            var retries = new AtomicInteger();

            TestUtil.vertx().setPeriodic(TimeUnit.SECONDS.toMillis(15), TimeUnit.SECONDS.toMillis(3), id ->
            {
                if (retries.getAndIncrement() < 10)
                {
                    var deployedVerticles = Bootstrap.getDeployedVerticles();

                    Assertions.assertNull(deployedVerticles.get(SNMPTrapProcessor.class.getSimpleName()));

                    Assertions.assertNull(deployedVerticles.get(ObjectManager.class.getSimpleName()));

                    Assertions.assertNull(deployedVerticles.get(DiscoveryEngine.class.getSimpleName()));

                    Assertions.assertNull(deployedVerticles.get(TopologyEngine.class.getSimpleName()));

                    Assertions.assertNull(deployedVerticles.get(MetricScheduler.class.getSimpleName()));

                    Assertions.assertNull(deployedVerticles.get(MetricPolicyTriggerDurationCalculator.class.getSimpleName()));

                    TestUtil.vertx().cancelTimer(id);

                    testContext.completeNow();
                }
                else
                {
                    TestUtil.vertx().cancelTimer(id);

                    testContext.failNow("unable to undeploy license verticles");
                }
            });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            testContext.failNow(exception);
        }
    }

    @Test
    @Order(22)
    void testInitStore(VertxTestContext testContext) throws IOException
    {
        var passoverConfigStores = Set.of(AbstractConfigStore.ConfigStore.METRIC);

        var passoverCacheStores = Set.of(AbstractCacheStore.CacheStore.OBJECT_MANAGER);

        var handlers = new ArrayList<Future<Void>>();

        for (var configStore : AbstractConfigStore.ConfigStore.values())
        {
            try
            {
                var store = AbstractConfigStore.getConfigStore(configStore);

                if (!passoverConfigStores.contains(configStore) && store != null)
                {
                    handlers.add(store.initStore());
                }
            }
            catch (Exception ignored)
            {
            }
        }

        for (var cacheStore : AbstractCacheStore.CacheStore.values())
        {
            try
            {
                var store = AbstractCacheStore.getCacheStore(cacheStore);

                if (!passoverCacheStores.contains(cacheStore) && store != null)
                {
                    handlers.add(store.initStore());
                }
            }
            catch (Exception ignored)
            {
            }
        }

        Future.join(handlers).onComplete(result ->
                testContext.completeNow());
    }

    @Test
    @Order(23)
    void testUpdateAllUsersv1(VertxTestContext testContext)
    {
        try
        {

            var items = UserConfigStore.getStore().getItems();

            JsonObject item = null;

            for (var i = 0; i < items.size(); i++)
            {
                if (!items.getJsonObject(i).getString(ConfigDBConstants.FIELD_TYPE).equalsIgnoreCase(ConfigDBConstants.ENTITY_TYPE_SYSTEM) && userId != items.getJsonObject(i).getLong(GlobalConstants.ID))
                {
                    item = items.getJsonObject(i);

                    break;
                }
            }

            if (item != null)
            {
                userId = item.getLong(GlobalConstants.ID);

                Bootstrap.configDBService().updateAll(ConfigDBConstants.COLLECTION_USER, new JsonObject().put(ConfigDBConstants.FIELD_NAME, User.USER_NAME).put(VALUE, new JsonArray().add(item.getString(User.USER_NAME))), new JsonObject().put(User.USER_GROUPS, new JsonArray().add(GlobalConstants.DEFAULT_ID)),
                        GlobalConstants.DEFAULT_USER, GlobalConstants.SYSTEM_REMOTE_ADDRESS, handler ->
                        {
                            try
                            {
                                TimeUnit.SECONDS.sleep(2);
                            }
                            catch (Exception ignored)
                            {
                            }


                            Bootstrap.configDBService().getOneByQuery(ConfigDBConstants.COLLECTION_USER, new JsonObject().put(ConfigDBConstants.FIELD_NAME, GlobalConstants.ID).put(VALUE, userId), response ->
                            {
                                Assertions.assertNotNull(response.result());

                                var user = response.result();

                                Assertions.assertEquals(user.getJsonArray(User.USER_GROUPS), new JsonArray().add(DEFAULT_ID));

                                testContext.completeNow();
                            });
                        });
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    @Test
    @Order(24)
    void testUpdateAllUsersv2(VertxTestContext testContext)
    {
        try
        {

            Bootstrap.configDBService().updateAll(ConfigDBConstants.COLLECTION_USER, null, new JsonObject().put(User.USER_GROUPS, new JsonArray().add(GlobalConstants.DEFAULT_ID)),
                    GlobalConstants.DEFAULT_USER, GlobalConstants.SYSTEM_REMOTE_ADDRESS, handler ->
                    {
                        try
                        {

                            TimeUnit.SECONDS.sleep(2);
                        }

                        catch (Exception ignored)
                        {

                        }


                        Bootstrap.configDBService().getOneByQuery(ConfigDBConstants.COLLECTION_USER, new JsonObject().put(ConfigDBConstants.FIELD_NAME, GlobalConstants.ID).put(VALUE, GlobalConstants.DEFAULT_ID), response ->
                        {
                            Assertions.assertNotNull(response.result());

                            Assertions.assertEquals(response.result().getJsonArray(User.USER_GROUPS), new JsonArray().add(GlobalConstants.DEFAULT_ID));

                            testContext.completeNow();
                        });
                    });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    @Test
    @Order(25)
    void testDeleteAllUsers(VertxTestContext testContext)
    {
        try
        {

            var items = UserConfigStore.getStore().getItems();

            JsonObject item = null;

            for (var i = 0; i < items.size(); i++)
            {
                if (!items.getJsonObject(i).getString(ConfigDBConstants.FIELD_TYPE).equalsIgnoreCase(ConfigDBConstants.ENTITY_TYPE_SYSTEM))
                {
                    item = items.getJsonObject(i);

                    break;
                }
            }

            if (item != null)
            {
                userId = item.getLong(GlobalConstants.ID);

                Bootstrap.configDBService().deleteAll(ConfigDBConstants.COLLECTION_USER, new JsonObject().put(ConfigDBConstants.FIELD_NAME, User.USER_NAME).put(VALUE, new JsonArray().add(item.getString(User.USER_NAME))),
                        GlobalConstants.DEFAULT_USER, GlobalConstants.SYSTEM_REMOTE_ADDRESS, result ->
                        {
                            try
                            {
                                Assertions.assertTrue(result.succeeded());

                                TimeUnit.SECONDS.sleep(2);
                            }
                            catch (Exception ignored)
                            {
                            }


                            Bootstrap.configDBService().getOneByQuery(ConfigDBConstants.COLLECTION_USER, new JsonObject().put(ConfigDBConstants.FIELD_NAME, GlobalConstants.ID).put(VALUE, userId), response ->
                            {
                                if (response == null || response.result() == null || response.result().isEmpty())
                                {
                                    testContext.completeNow();
                                }
                            });
                        });
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    // ObjectManager and ConfigManager were undeployed in 'testLicenceExpiryAndUndeployLicensedVerticles' and need to be redeployed for the delete request.
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(26)
    void testDeleteObject(VertxTestContext testContext)
    {
        Bootstrap.startEngine(new ObjectManager(), ObjectManager.class.getSimpleName(), null)
                .compose(future -> Bootstrap.startEngine(new ConfigManager(), ConfigManager.class.getSimpleName(), null))
                .onComplete(result ->
                {
                    if (result.succeeded())
                    {
                        var ids = ObjectConfigStore.getStore().getIds();

                        Assertions.assertNotNull(ids);

                        Assertions.assertFalse(ids.isEmpty());

                        // no need to call delete config object separately -- ObjectManager make the delete request to ConfigManager
                        TestAPIUtil.deleteAll(OBJECT_API_ENDPOINT, new JsonObject()
                                .put(REQUEST_PARAM_IDS, ids), testContext.succeeding(response ->
                                testContext.verify(() ->
                                {
                                    assertEquals(SC_BAD_REQUEST, response.statusCode());

                                    // 10-second timer has been introduced to ensure proper completion of the test case before the stop method is executed.
                                    TestUtil.vertx().setTimer(TimeUnit.SECONDS.toMillis(10), timer ->
                                            testContext.completeNow());
                                })));
                    }
                    else
                    {
                        testContext.failNow(result.cause());
                    }

                });
    }

    @Test
    @Order(27)
    void testBackupFlapCache(VertxTestContext testContext)
    {
        try
        {
            new FlapCacheBackupJob().execute(null);

            TimeUnit.SECONDS.sleep(3);

            var files = FileUtils.listFiles(new File(ConfigDBConstants.CONFIG_DB_BACKUP_PATH + PATH_SEPARATOR + "flap-caches"), new String[]{"zip"}, true);

            Assertions.assertNotNull(files);

            Assertions.assertNotEquals(0, files.size());

            testContext.completeNow();
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

        }

    }

    @Test
    @Order(28)
    void testCleanupFlapCache(VertxTestContext testContext)
    {
        try
        {
            var backupDir = new File(CURRENT_DIR + PATH_SEPARATOR + "config-db-backups" + PATH_SEPARATOR + "flap-caches");

            var oldFile = new File(backupDir + PATH_SEPARATOR + "flap-cache" + DASH_SEPARATOR + "1262284200000.zip");

            oldFile.createNewFile();

            new DailyMiscJobs().cleanupFlapCache(1);

            assertFalse(oldFile.exists());

            testContext.completeNow();

        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

    }

    @Test
    @Order(29)
    void testChangeLogLevelTraceAndGet(VertxTestContext testContext) throws Exception
    {
        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
        {

            if (message.body().containsKey(EVENT_TYPE) && message.body().getString(EVENT_TYPE).equalsIgnoreCase(UI_ACTION_LOGGER_LEVEL_GET) && message.body().getBinary(EVENT_CONTEXT) != null)
            {
                var context = CodecUtil.toJSONObject(message.body().getBinary(EventBusConstants.EVENT_CONTEXT));

                LOGGER.debug("Received context from log level change and get : " + context.encode());

                Assertions.assertEquals(HttpStatus.SC_OK, context.getInteger(RESPONSE_CODE));

                Assertions.assertEquals(STATUS_SUCCEED, context.getString(STATUS));

                Assertions.assertEquals(0, context.getInteger(SYSTEM_LOG_LEVEL));

                Assertions.assertTrue(context.getJsonObject(SYSTEM_LOG_MODULES).getBoolean(MOTADATA_LOG));

                Assertions.assertTrue(context.getJsonObject(SYSTEM_LOG_MODULES).getBoolean(MOTADATA_FLOW));

                messageConsumer.unregister(result -> testContext.completeNow());
            }
        });

        LOGGER.info(String.format("current log level : %s ", CommonUtil.getLogLevel()));

        TestUtil.vertx().eventBus().send(UI_ACTION_LOGGER_LEVEL_CHANGE, new JsonObject().put(APIConstants.SESSION_ID, TestUtil.getSessionId()).put(USERNAME, "admin").put(SYSTEM_LOG_LEVEL, 0).put("log.level.reset.timer.seconds", 10).put(SYSTEM_LOG_MODULES, new JsonArray().add(MOTADATA_LOG).add(MOTADATA_FLOW)).put(REMOTE_EVENT_PROCESSOR_UUID, Bootstrap.getRegistrationId()).put(REMOTE_EVENT_PROCESSOR_TYPE, "APP"));

        testContext.awaitCompletion(5, TimeUnit.SECONDS);

        TestUtil.vertx().eventBus().send(UI_ACTION_LOGGER_LEVEL_GET, new JsonObject().put(APIConstants.SESSION_ID, TestUtil.getSessionId()));

    }
}