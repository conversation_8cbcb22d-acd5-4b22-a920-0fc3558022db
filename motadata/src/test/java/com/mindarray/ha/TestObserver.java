/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.ha;

import com.mindarray.Bootstrap;
import com.mindarray.TestUtil;
import com.mindarray.api.BackupProfile;
import com.mindarray.api.CredentialProfile;
import com.mindarray.api.User;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.nms.NMSConstants;
import com.mindarray.util.*;
import io.github.artsok.RepeatedIfExceptionsTest;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.eventbus.MessageConsumer;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.junit5.Timeout;
import io.vertx.junit5.VertxExtension;
import io.vertx.junit5.VertxTestContext;
import org.apache.commons.io.FileUtils;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.extension.ExtendWith;
import org.zeromq.SocketType;
import org.zeromq.ZMQ;

import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.AIOpsObject.OBJECT_ID;
import static com.mindarray.api.APIConstants.ENTITY_COLLECTION;
import static com.mindarray.api.CredentialProfile.CREDENTIAL_PROFILE_NAME;
import static com.mindarray.api.CredentialProfile.CREDENTIAL_PROFILE_PROTOCOL;
import static com.mindarray.api.RemoteEventProcessor.*;
import static com.mindarray.compliance.ComplianceConstants.*;
import static com.mindarray.datastore.DatastoreConstants.DATASTORE_WRITER_MAX_BUFFER_BYTES;
import static com.mindarray.db.ConfigDBConstants.*;
import static com.mindarray.eventbus.EventBusConstants.*;
import static com.mindarray.ha.HAConstants.CACHE_NAME;
import static com.mindarray.ha.HAConstants.HA_SYNC_OPERATION;
import static com.mindarray.nms.NMSConstants.Type.POWERSHELL;
import static org.junit.jupiter.api.Assertions.assertTrue;

@ExtendWith(VertxExtension.class)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@EnabledIfSystemProperty(named = "test.mode", matches = "^OBSERVER$|^PRIMARY|^SECONDARY$")
@Timeout(100 * 1000)
public class TestObserver
{
    private static final Logger LOGGER = new Logger(TestObserver.class, MOTADATA_HA, "Test Observer");
    private static final ZMQ.Socket SUBSCRIBER = Bootstrap.zcontext().socket(SocketType.SUB);
    private static final AtomicBoolean HAS_MORE_EVENTS = new AtomicBoolean(true);
    private static MessageConsumer<JsonObject> messageConsumer;

    private static VertxTestContext VERTX_TEST_CONTEXT;
    private static String TEST_CASE_NAME;

    private static void subscriber()
    {
        SUBSCRIBER.setHWM(MotadataConfigUtil.getEventBacklogSize());

        SUBSCRIBER.setRcvHWM(MotadataConfigUtil.getEventBacklogSize());

        SUBSCRIBER.subscribe("secondary_app_uuid.remote.event.processor");

        SUBSCRIBER.connect("tcp://" + MotadataConfigUtil.getRemoteEventObserverPublisher() + ":" + MotadataConfigUtil.getMotadataObserverEventPublisherPort());

        new Thread(() ->
        {
            byte[] bufferBytes;

            byte[] topicBytes;

            while (HAS_MORE_EVENTS.get())
            {
                try
                {
                    topicBytes = SUBSCRIBER.recv();

                    bufferBytes = SUBSCRIBER.recv();

                    if (bufferBytes.length > 0)
                    {
                        LOGGER.info(String.format("receiving on %s port ", MotadataConfigUtil.getMotadataObserverEventPublisherPort()));

                    }

                    if (VERTX_TEST_CONTEXT != null && TEST_CASE_NAME.equals("testConfigRead"))
                    {
                        LOGGER.info(TEST_CASE_NAME + ": result: " + Arrays.toString(bufferBytes));

                        var buffer = Buffer.buffer().appendShortLE(CommonUtil.getShort(topicBytes.length)).appendBytes(topicBytes).appendBytes(bufferBytes);

                        var event = CodecUtil.toJSONObject(new CipherUtil().decrypt(buffer.getBytes(2 + buffer.getShortLE(0), buffer.length())));

                        try
                        {
                            var result = TestUtil.decodeEvent(event);

                            LOGGER.info(TEST_CASE_NAME + ": result: " + result.encode());

                            if (result.getJsonArray(DB_DOCUMENTS).getJsonObject(0).getString(CREDENTIAL_PROFILE_NAME).equalsIgnoreCase("test HA"))
                            {
                                assertTrue(result.getString(ENTITY_COLLECTION).equalsIgnoreCase(COLLECTION_CREDENTIAL_PROFILE));

                                assertTrue(result.getJsonArray(DB_DOCUMENTS).getJsonObject(0).getString(CREDENTIAL_PROFILE_NAME).equalsIgnoreCase("test HA"));

                                assertTrue(result.getJsonArray(DB_DOCUMENTS).getJsonObject(0).getString(CREDENTIAL_PROFILE_PROTOCOL).equalsIgnoreCase(NMSConstants.Protocol.POWERSHELL.getName()));

                                VERTX_TEST_CONTEXT.completeNow();
                            }
                        }
                        catch (Exception exception)
                        {
                            LOGGER.error(exception);
                        }
                    }
                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);
                }
            }

            LOGGER.warn("Remote Event Subscriber disconnected.....");

        }, "Test Observer Remote Event Subscriber APP").start();
    }

    @BeforeAll
    static void beforeAll(VertxTestContext testContext)
    {
        subscriber();

        testContext.completeNow();
    }

    @AfterAll
    static void afterAll(VertxTestContext testContext)
    {
        HAS_MORE_EVENTS.set(false);

        SUBSCRIBER.close();

        testContext.completeNow();
    }

    @BeforeEach
    void beforeEach(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running test case %s", testInfo.getTestMethod().get().getName()));

        VERTX_TEST_CONTEXT = null;

        TEST_CASE_NAME = null;

        if (messageConsumer != null)
        {
            messageConsumer.unregister(result -> testContext.completeNow());
        }
        else
        {
            testContext.completeNow();
        }
    }

    @Order(1)
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @EnabledIfSystemProperty(named = "test.mode", matches = "OBSERVER")
    void testEventRegistration(VertxTestContext testContext, TestInfo testInfo)
    {
        var primaryAppContext = new JsonObject()
                .put(EVENT_TYPE, EVENT_REGISTRATION)
                .put(REMOTE_EVENT_PROCESSOR_HOST, "test-case-app-primary")
                .put(REMOTE_EVENT_PROCESSOR_INSTALLATION_MODE, InstallationMode.PRIMARY.name())
                .put(REMOTE_EVENT_PROCESSOR_TYPE, BootstrapType.APP.name())
                .put(REMOTE_EVENT_PROCESSOR_IP, "*************")
                .put(REMOTE_EVENT_PROCESSOR_UUID, "primary_app_uuid")
                .put(REMOTE_EVENT_PROCESSOR_VERSION, "8.0.1");

        var secondaryAppContext = new JsonObject()
                .put(EVENT_TYPE, EVENT_REGISTRATION)
                .put(REMOTE_EVENT_PROCESSOR_HOST, "test-case-app-secondary")
                .put(REMOTE_EVENT_PROCESSOR_INSTALLATION_MODE, InstallationMode.PRIMARY.name())
                .put(REMOTE_EVENT_PROCESSOR_TYPE, BootstrapType.APP.name())
                .put(REMOTE_EVENT_PROCESSOR_IP, "*************")
                .put(REMOTE_EVENT_PROCESSOR_UUID, "secondary_app_uuid")
                .put(REMOTE_EVENT_PROCESSOR_VERSION, "8.0.1");

        TestUtil.vertx().eventBus().send(EVENT_REGISTRATION, primaryAppContext);

        TestUtil.vertx().eventBus().send(EVENT_REGISTRATION, secondaryAppContext);

        TestUtil.vertx().setTimer(5 * 1000, timerId ->
        {
            try
            {
                var buffer = FileUtils.readFileToString(new File(CURRENT_DIR + PATH_SEPARATOR + LOGS_DIR + PATH_SEPARATOR + "ha" + PATH_SEPARATOR + "$$$-Observer.log".replace("$$$", new SimpleDateFormat("dd-MMMM-yyyy HH").format(new Date()))), StandardCharsets.UTF_8);

                LOGGER.info(testInfo.getTestMethod().get().getName() + ": " + buffer);

                if (buffer.contains("primary_app_uuid app registration event completed...") && buffer.contains("secondary_app_uuid app registration event completed..."))
                {
                    testContext.completeNow();
                }
                else
                {
                    testContext.failNow("there is no logs for registration");
                }
            }
            catch (IOException exception)
            {
                LOGGER.error(exception);

                testContext.failNow(exception);
            }
        });
    }

    @Order(2)
    @RepeatedIfExceptionsTest(suspend = 3000)
    @EnabledIfSystemProperty(named = "test.mode", matches = "OBSERVER")
    void testConfigUpdate(VertxTestContext testContext) throws InterruptedException
    {
        testContext.awaitCompletion(3, TimeUnit.SECONDS);

        var context = new JsonObject("""
                {
                  "event.timestamp" : 1715585542,
                  "event.context" : "BsJBZgAAAAAyAQAAeyJkYi5kb2N1bWVudHMiOlt7ImNyZWRlbnRpYWwucHJvZmlsZS5uYW1lIjoidGVzdCBIQSIsImNyZWRlbnRpYWwucHJvZmlsZS5wcm90b2NvbCI6IlBvd2Vyc2hlbGwiLCJjcmVkZW50aWFsLnByb2ZpbGUuY29udGV4dCI6eyJ1c2VybmFtZSI6InRlc3QiLCJwYXNzd29yZCI6InRlc3QifSwiX3R5cGUiOiIxIiwiaWQiOjQzMDU0MDc2ODAwfV0sImNvbGxlY3Rpb24iOiJjcmVkZW50aWFsLnByb2ZpbGUiLCJ1c2VyLm5hbWUiOiJhZG1pbiIsInJlbW90ZS5hZGRyZXNzIjoiMDowOjA6MDowOjA6MDoxIiwiaGEuc3luYy5vcGVyYXRpb24iOjB9"
                }""");

        TestUtil.vertx().eventBus().send(EVENT_HA_CONFIG_OBSERVER_SYNC, context.put(HA_SYNC_OPERATION, HAConstants.HASyncOperation.SAVE.getName())
                .put(EVENT_TYPE, EVENT_HA_CONFIG_OBSERVER_SYNC).put(REMOTE_EVENT_PROCESSOR_UUID, "primary_app_uuid"));

        testContext.awaitCompletion(5, TimeUnit.SECONDS);

        testContext.completeNow();
    }

    @Order(3)
    @RepeatedIfExceptionsTest(suspend = 3000)
    @EnabledIfSystemProperty(named = "test.mode", matches = "OBSERVER")
    void testConfigRead(VertxTestContext testContext, TestInfo testInfo)
    {
        TEST_CASE_NAME = testInfo.getTestMethod().get().getName();

        VERTX_TEST_CONTEXT = testContext;

        var context = new JsonObject()
                .put(HA_SYNC_OPERATION, HAConstants.HASyncOperation.READ.getName())
                .put(INSTALLATION_MODE, InstallationMode.PRIMARY.name())
                .put(REMOTE_EVENT_PROCESSOR_UUID, "secondary_app_uuid")
                .put(DATASTORE_WRITER_MAX_BUFFER_BYTES, 1048576);

        TestUtil.vertx().setPeriodic(5 * 1000, timer -> TestUtil.vertx().eventBus().send(EVENT_HA_CONFIG_OBSERVER_SYNC, context));
    }

    @Order(4)
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @EnabledIfSystemProperty(named = "test.mode", matches = "OBSERVER")
    void testSyncWriteHACacheManager(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = new JsonObject()
                .put(CACHE_NAME, "test-cases-cache-file")
                .put(RESULT, "QHYW3siZHVyYXRpb24iOjE3MTE2NTcwNDYsInN0YXR1cyI6IlVwIiwiZXZlbnQudGltZXN0YW1wIg0rPDk0OTYsImhlYXJ0YmVhdC4BNYBlIjoiUnVubmluZyIsImlkIjo3NjIxNzY5ODgyNDUwfSxKcgAIMTIwnnIACDUwMLZyAAgxfV0")
                .put(HA_SYNC_OPERATION, HAConstants.HASyncOperation.WRITE.getName())
                .put(REMOTE_EVENT_PROCESSOR_UUID, "primary_app_uuid")
                .put(EVENT_TIMESTAMP, 1711659523)
                .put(EVENT_COPY_REQUIRED, false)
                .put(EVENT_TYPE, EVENT_HA_CACHE_OBSERVER_SYNC);

        TestUtil.vertx().eventBus().send(EVENT_HA_CACHE_OBSERVER_SYNC, context);

        TestUtil.vertx().setTimer(5 * 1000, handler ->
        {
            var configDir = new File(CURRENT_DIR + PATH_SEPARATOR + "events" + PATH_SEPARATOR + EventBusConstants.replace(EVENT_HA_CONFIG_MANGER_SYNC) + "-secondary_app_uuid");

            LOGGER.info(testInfo.getTestMethod().get().getName() + ": config files: " + Arrays.toString(configDir.list()));

            for (var fileName : Objects.requireNonNull(configDir.list()))
            {
                if (fileName.equalsIgnoreCase("test-cases-cache-file"))
                {
                    testContext.completeNow();
                }
            }
        });
    }

    @Order(5)
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @EnabledIfSystemProperty(named = "test.mode", matches = "OBSERVER")
    void testSyncReadHACacheManager(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = new JsonObject()
                .put(EVENT_TYPE, EVENT_HA_CACHE_OBSERVER_SYNC)
                .put(HA_SYNC_OPERATION, HAConstants.HASyncOperation.READ.getName())
                .put(INSTALLATION_MODE, InstallationMode.SECONDARY.name())
                .put(REMOTE_EVENT_PROCESSOR_UUID, "secondary_app_uuid");

        TestUtil.vertx().eventBus().send(EVENT_HA_CACHE_OBSERVER_SYNC, context);

        TestUtil.vertx().setTimer(10 * 1000, handler ->
        {
            var configDir = new File(CURRENT_DIR + PATH_SEPARATOR + "events" + PATH_SEPARATOR + EventBusConstants.replace(EVENT_HA_CONFIG_MANGER_SYNC) + "-secondary_app_uuid");

            LOGGER.info(testInfo.getTestMethod().get().getName() + ": ConfigDir: " + Arrays.toString(configDir.list()));

            var flag = false;

            for (var fileName : Objects.requireNonNull(configDir.list()))
            {
                if (fileName.equalsIgnoreCase("test-cases-cache-file"))
                {
                    flag = true;

                    break;
                }
            }

            if (flag)
            {
                testContext.failNow("file still exists");
            }
            else
            {
                testContext.completeNow();
            }
        });
    }

    @Order(6)
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @EnabledIfSystemProperty(named = "test.mode", matches = "^PRIMARY|^SECONDARY$")
    void testSyncFlushConfig1(VertxTestContext testContext, TestInfo testInfo)
    {
        var event = new JsonObject("{\"event.timestamp\":1715595586,\"event.context\":\"QulBZgAAAAA7AQAAeyJkYi5kb2N1bWVudHMiOlt7ImNyZWRlbnRpYWwucHJvZmlsZS5uYW1lIjoidGVzdGNhc2UiLCJjcmVkZW50aWFsLnByb2ZpbGUucHJvdG9jb2wiOiJQb3dlcnNoZWxsIiwiY3JlZGVudGlhbC5wcm9maWxlLmNvbnRleHQiOnsidXNlcm5hbWUiOiJ0ZXN0Y2FzZSIsInBhc3N3b3JkIjoidGVzdGNhc2UifSwiX3R5cGUiOiIxIiwiaWQiOjQzMDU0MDc2ODAzfV0sImNvbGxlY3Rpb24iOiJjcmVkZW50aWFsX3Byb2ZpbGUiLCJ1c2VyLm5hbWUiOiJhZG1pbiIsInJlbW90ZS5hZGRyZXNzIjoiMDowOjA6MDowOjA6MDoxIiwiaGEuc3luYy5vcGVyYXRpb24iOjB9\",\"ha.sync.operation\":0,\"event.topic\":\"ha.observer\",\"remote.event.processor.uuid\":\"fiebfe\",\"event.type\":\"ha.config.observer.sync\"}");

        var buffer = Buffer.buffer(event.getBinary(EVENT_CONTEXT));

        var context = new JsonObject(new String(buffer.getBytes(12, 12 + buffer.getIntLE(8)))).put(DATABASE, BackupProfile.BackupProfileType.CONFIG_DB.getName());

        var bytes = context.encode().getBytes();

        TestUtil.vertx().eventBus().send(EVENT_HA_CONFIG_OBSERVER_SYNC, event.put(EVENT_CONTEXT, Buffer.buffer("UTF-8").setLongLE(0, event.getLong(EVENT_TIMESTAMP)).appendIntLE(bytes.length).appendBytes(bytes)));

        var counter = new AtomicInteger(0);

        TestUtil.vertx().setPeriodic(5 * 1000, timer ->
        {
            if (counter.get() == 3)
            {
                TestUtil.vertx().cancelTimer(timer);

                testContext.failNow("maximum attempts exceeded");
            }
            else
            {
                Bootstrap.configDBService().getAll(COLLECTION_CREDENTIAL_PROFILE, result ->
                {
                    if (result.succeeded())
                    {
                        for (var index = 0; index < result.result().size(); index++)
                        {
                            var credentialProfile = result.result().getJsonObject(index);

                            if (credentialProfile.getString(CREDENTIAL_PROFILE_NAME).equalsIgnoreCase("testcase"))
                            {
                                Assertions.assertEquals(credentialProfile.getString(CREDENTIAL_PROFILE_PROTOCOL), POWERSHELL.getName());

                                TestUtil.vertx().cancelTimer(timer);

                                testContext.completeNow();
                            }
                        }
                    }
                    else
                    {
                        LOGGER.info(testInfo.getTestMethod().get().getName() + ": handler.cause: " + result.cause());
                    }

                    counter.incrementAndGet();
                });
            }
        });
    }

    @Order(7)
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @EnabledIfSystemProperty(named = "test.mode", matches = "^PRIMARY|^SECONDARY$")
    void testSyncFlushConfig2(VertxTestContext testContext, TestInfo testInfo)
    {
        var event = new JsonObject("{\"event.timestamp\":1715595855,\"event.context\":\"T-pBZgAAAABxAQAAeyJkYi5xdWVyeSI6eyJmaWVsZCI6ImlkIiwidmFsdWUiOjQzMDU0MDc2ODAzfSwiZGIuZG9jdW1lbnRzIjpbeyJjcmVkZW50aWFsLnByb2ZpbGUubmFtZSI6InRlc3RjYXNlLXVwZGF0ZWQiLCJjcmVkZW50aWFsLnByb2ZpbGUucHJvdG9jb2wiOiJQb3dlcnNoZWxsIiwiY3JlZGVudGlhbC5wcm9maWxlLmNvbnRleHQiOnsidXNlcm5hbWUiOiJ0ZXN0Y2FzZSIsInBhc3N3b3JkIjoidGVzdGNhc2UifSwiX3R5cGUiOiIxIiwiaWQiOjQzMDU0MDc2ODAzfV0sImNvbGxlY3Rpb24iOiJjcmVkZW50aWFsX3Byb2ZpbGUiLCJ1c2VyLm5hbWUiOiJhZG1pbiIsInJlbW90ZS5hZGRyZXNzIjoiMDowOjA6MDowOjA6MDoxIiwiaGEuc3luYy5vcGVyYXRpb24iOjJ9\",\"ha.sync.operation\":0,\"event.topic\":\"ha.observer\",\"remote.event.processor.uuid\":\"fiebfe\",\"event.type\":\"ha.config.observer.sync\"}");

        var buffer = Buffer.buffer(event.getBinary(EVENT_CONTEXT));

        var context = new JsonObject(new String(buffer.getBytes(12, 12 + buffer.getIntLE(8)))).put(DATABASE, BackupProfile.BackupProfileType.CONFIG_DB.getName());

        var bytes = context.encode().getBytes();

        TestUtil.vertx().eventBus().send(EVENT_HA_CONFIG_OBSERVER_SYNC, event.put(EVENT_CONTEXT, Buffer.buffer("UTF-8").setLongLE(0, event.getLong(EVENT_TIMESTAMP)).appendIntLE(bytes.length).appendBytes(bytes)));

        var counter = new AtomicInteger(0);

        TestUtil.vertx().setPeriodic(5 * 1000, timer ->
        {
            if (counter.get() == 3)
            {
                TestUtil.vertx().cancelTimer(timer);

                testContext.failNow("maximum attempts exceeded");
            }
            else
            {
                Bootstrap.configDBService().getAll(COLLECTION_CREDENTIAL_PROFILE, result ->
                {
                    if (result.succeeded())
                    {
                        for (var index = 0; index < result.result().size(); index++)
                        {
                            var credentialProfile = result.result().getJsonObject(index);

                            if (credentialProfile.getString(CREDENTIAL_PROFILE_NAME).equalsIgnoreCase("testcase-updated"))
                            {
                                Assertions.assertEquals(credentialProfile.getString(CREDENTIAL_PROFILE_PROTOCOL), POWERSHELL.getName());

                                TestUtil.vertx().cancelTimer(timer);

                                testContext.completeNow();
                            }
                        }
                    }
                    else
                    {
                        LOGGER.info(testInfo.getTestMethod().get().getName() + ": handler.cause: " + result.cause());
                    }

                    counter.incrementAndGet();
                });
            }
        });
    }

    @Order(8)
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @EnabledIfSystemProperty(named = "test.mode", matches = "^PRIMARY|^SECONDARY$")
    void testSyncFlushConfig3(VertxTestContext testContext, TestInfo testInfo)
    {
        var event = new JsonObject("{\"event.timestamp\":1715596143,\"event.context\":\"b-tBZgAAAACeAAAAeyJkYi5xdWVyeSI6eyJmaWVsZCI6ImlkIiwidmFsdWUiOjQzMDU0MDc2ODAzfSwiY29sbGVjdGlvbiI6ImNyZWRlbnRpYWxfcHJvZmlsZSIsInVzZXIubmFtZSI6ImFkbWluIiwicmVtb3RlLmFkZHJlc3MiOiIwOjA6MDowOjA6MDowOjEiLCJoYS5zeW5jLm9wZXJhdGlvbiI6NH0\",\"ha.sync.operation\":0,\"event.topic\":\"ha.observer\",\"remote.event.processor.uuid\":\"fiebfe\",\"event.type\":\"ha.config.observer.sync\"}");

        var buffer = Buffer.buffer(event.getBinary(EVENT_CONTEXT));

        var context = new JsonObject(new String(buffer.getBytes(12, 12 + buffer.getIntLE(8)))).put(DATABASE, BackupProfile.BackupProfileType.CONFIG_DB.getName());

        var bytes = context.encode().getBytes();

        TestUtil.vertx().eventBus().send(EVENT_HA_CONFIG_OBSERVER_SYNC, event.put(EVENT_CONTEXT, Buffer.buffer("UTF-8").setLongLE(0, event.getLong(EVENT_TIMESTAMP)).appendIntLE(bytes.length).appendBytes(bytes)));

        var counter = new AtomicInteger(0);

        TestUtil.vertx().setPeriodic(5 * 1000, timer ->
        {
            if (counter.get() == 3)
            {
                testContext.failNow("maximum attempts exceeded");

                TestUtil.vertx().cancelTimer(timer);
            }
            else
            {
                Bootstrap.configDBService().getAll(COLLECTION_CREDENTIAL_PROFILE, result ->
                {
                    if (result.succeeded())
                    {
                        var available = false;

                        for (var index = 0; index < result.result().size(); index++)
                        {
                            var credentialProfile = result.result().getJsonObject(index);

                            if (credentialProfile.getString(CREDENTIAL_PROFILE_NAME).equalsIgnoreCase("testcase-updated"))
                            {
                                available = true;

                                testContext.completeNow();
                            }
                        }

                        if (!available)
                        {
                            testContext.completeNow();
                        }
                        else
                        {
                            testContext.failNow("credential profile still exist");
                        }
                    }
                    else
                    {
                        LOGGER.info(testInfo.getTestMethod().get().getName() + ": handler.cause: " + result.cause());
                    }

                    counter.incrementAndGet();
                });
            }
        });
    }

    @Order(9)
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @EnabledIfSystemProperty(named = "test.mode", matches = "^PRIMARY|^SECONDARY$")
    void testSyncFlushCompliance1(VertxTestContext testContext)
    {
        try
        {
            var timestamp = DateTimeUtil.currentSeconds();

            var id = 123L;

            var buffer = Buffer.buffer("UTF-8");

            for (var index = 0; index < 3; index++)
            {
                LOGGER.info(String.format("SAVE operation for compliance id : %s ", id + index));

                var bytes = new JsonObject().put(DB_DOCUMENTS, new JsonArray().add(new JsonObject().put(SCANNED_RULE, 1).put(COMPLIANCE_POLICY_ID, id + index).put(OBJECT_ID, 1).put(LAST_SCAN_STATUS, 1).put(MESSAGE, "Scan Completed Successfully").put(COMPLIANCE_PERCENTAGE, 100).put(SEVERITY, "secure").put(LAST_SCAN_TIMESTAMP, DateTimeUtil.currentSeconds() - 5))).put(ENTITY_COLLECTION, TABLE_COMPLIANCE_STATS_ENTITY).put(User.USER_NAME, "admin").put(REMOTE_ADDRESS, "127.0.0.1").put(HAConstants.HA_SYNC_OPERATION, HAConstants.HASyncOperation.SAVE_ALL.getName()).put(DATABASE, COMPLIANCE_DB).encode().getBytes();

                buffer.setLongLE(0, timestamp).appendIntLE(bytes.length).appendBytes(bytes);

                TestUtil.vertx().eventBus().send(EVENT_HA_CONFIG_OBSERVER_SYNC, new JsonObject().put(EVENT_TIMESTAMP, timestamp).put(EVENT_CONTEXT, buffer.getBytes()));
            }

            testContext.awaitCompletion(5, TimeUnit.SECONDS);

            Bootstrap.configDBService().select(TABLE_COMPLIANCE_STATS_ENTITY, new JsonObject().put(TRANSFORM, false).put(QUERY, "SELECT * FROM tbl_compliance_stats_entity"), asyncResult ->
            {
                try
                {
                    if (asyncResult.succeeded() && asyncResult.result() != null)
                    {
                        LOGGER.info(String.format("result received : %s ", asyncResult.result()));

                        var rows = asyncResult.result().getJsonArray(RESULT);

                        var valid = false;

                        Assertions.assertNotNull(rows);

                        for (var index = 0; index < rows.size(); index++)
                        {
                            var row = rows.getJsonObject(index);

                            if (row.getLong(COMPLIANCE_POLICY_ID).equals(id))
                            {
                                valid = true;

                                break;
                            }
                        }

                        if (valid)
                        {
                            testContext.completeNow();
                        }
                        else
                        {
                            testContext.failNow("compliance policy result is not saved");
                        }
                    }
                    else
                    {
                        LOGGER.error(asyncResult.cause().getCause());
                    }
                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);

                    testContext.failNow(exception);
                }
            });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            testContext.failNow(exception);
        }
    }

    @Order(10)
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @EnabledIfSystemProperty(named = "test.mode", matches = "^PRIMARY|^SECONDARY$")
    void testSyncFlushCompliance2(VertxTestContext testContext)
    {
        try
        {
            var timestamp = DateTimeUtil.currentSeconds();

            var id = 123L;

            var buffer = Buffer.buffer("UTF-8");

            var bytes = new JsonObject().put(DB_QUERY, new JsonObject().put(COMPLIANCE_POLICY_ID.replace(".", "_"), id)).put(ENTITY_COLLECTION, TABLE_COMPLIANCE_STATS_ENTITY).put(User.USER_NAME, "admin").put(REMOTE_ADDRESS, "127.0.0.1").put(HAConstants.HA_SYNC_OPERATION, HAConstants.HASyncOperation.DELETE.getName()).put(DATABASE, COMPLIANCE_DB).encode().getBytes();

            buffer.setLongLE(0, timestamp).appendIntLE(bytes.length).appendBytes(bytes);

            TestUtil.vertx().eventBus().send(EVENT_HA_CONFIG_OBSERVER_SYNC, new JsonObject().put(EVENT_TIMESTAMP, timestamp).put(EVENT_CONTEXT, buffer.getBytes()));

            testContext.awaitCompletion(5, TimeUnit.SECONDS);

            TestUtil.vertx().eventBus().send(EVENT_HA_CONFIG_OBSERVER_SYNC, new JsonObject().put(EVENT_TIMESTAMP, timestamp).put(EVENT_CONTEXT, buffer.getBytes()));

            testContext.awaitCompletion(5, TimeUnit.SECONDS);

            Bootstrap.configDBService().select(TABLE_COMPLIANCE_STATS_ENTITY, new JsonObject().put(TRANSFORM, false).put(QUERY, "SELECT * FROM tbl_compliance_stats_entity"), asyncResult ->
            {
                try
                {
                    if (asyncResult.succeeded() && asyncResult.result() != null)
                    {
                        if (!asyncResult.result().isEmpty())
                        {
                            LOGGER.info(String.format("result received : %s ", asyncResult.result()));

                            var rows = asyncResult.result().getJsonArray(RESULT);

                            var valid = true;

                            Assertions.assertNotNull(rows);

                            for (var index = 0; index < rows.size(); index++)
                            {
                                var row = rows.getJsonObject(index);

                                if (row.getLong(COMPLIANCE_POLICY_ID).equals(id))
                                {
                                    LOGGER.info(String.format("row : %s ", row.encode()));

                                    valid = false;

                                    break;
                                }
                            }

                            if (valid)
                            {
                                testContext.completeNow();
                            }
                            else
                            {
                                testContext.failNow("compliance policy result is not deleted yet");
                            }
                        }
                        else
                        {
                            testContext.completeNow();
                        }
                    }
                    else
                    {
                        LOGGER.error(asyncResult.cause().getCause());

                        testContext.failNow(asyncResult.cause());
                    }
                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);

                    testContext.failNow(exception);
                }
            });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            testContext.failNow(exception);
        }
    }

    @Order(11)
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @EnabledIfSystemProperty(named = "test.mode", matches = "MASTER")
    void testConfigDatastoreWrite(VertxTestContext testContext, TestInfo testInfo)
    {
        messageConsumer = TestUtil.vertx().eventBus().localConsumer("test.config.datastore.manager", message ->
        {
            var result = TestUtil.decodeEvent(message.body());

            LOGGER.info(testInfo.getTestMethod().get().getName() + ": result: " + result.encode());

            if (result.getString("collection").equalsIgnoreCase(COLLECTION_CREDENTIAL_PROFILE))
            {
                var document = result.getJsonArray(DB_DOCUMENTS).getJsonObject(0);

                Assertions.assertTrue(document.getString(CredentialProfile.CREDENTIAL_PROFILE_NAME).contains("testcase"));

                Assertions.assertTrue(document.getString(CredentialProfile.CREDENTIAL_PROFILE_PROTOCOL).equalsIgnoreCase(NMSConstants.Protocol.POWERSHELL.getName()));

                messageConsumer.unregister();

                testContext.completeNow();
            }
        });

        for (var index = 0; index < 10; index++)
        {
            HAConstants.sync(COLLECTION_CREDENTIAL_PROFILE, new JsonArray().add(new JsonObject("{\"credential.profile.name\":\"testcase-" + index + "\",\"credential.profile.protocol\":\"Powershell\",\"credential.profile.context\":{\"username\":\"testcase-user\",\"password\":\"password\"},\"_type\":\"1\",\"id\":15452470851500}")), "admin", HAConstants.HASyncOperation.UPDATE.getName(), "Config DB");
        }

        TestUtil.vertx().setPeriodic(3 * 1000, timer ->
                TestUtil.vertx().eventBus().send(EVENT_HA_CONFIG_MANGER_SYNC + DOT_SEPARATOR + Bootstrap.getRegistrationId() + ".active", new JsonObject().put(HA_SYNC_OPERATION, HAConstants.HASyncOperation.READ.getName())));
    }

    @Order(12)
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @EnabledIfSystemProperty(named = "test.mode", matches = "^PRIMARY|^SECONDARY$")
    void testConfigSyncFlushCache(VertxTestContext testContext)
    {
        var context = new JsonObject().put(EVENT_TOPIC, EVENT_REMOTE_EVENT_PROCESSOR)
                .put(EVENT_COPY_REQUIRED, false)
                .put(REMOTE_EVENT_PROCESSOR_UUID, "primary_app_uuid")
                .put(EVENT_TYPE, EVENT_HA_CACHE_OBSERVER_SYNC)
                .put(CACHE_NAME, "test-cache-file")
                .put(HA_SYNC_OPERATION, HAConstants.HASyncOperation.READ.getName())
                .put(RESULT, "5wHYW3siZHVyYXRpb24iOjE3MTE0Mzk1NjIsInN0YXR1cyI6IlVwIiwiZXZlbnQudGltZXN0YW1wIgkrQDQwMDkyLCJoZWFydGJlYXQuATWIZSI6IlJ1bm5pbmciLCJpZCI6MTA3NDgyMTkyMzgzODV9LHs-cwAQNDAxMDCecwAFK7JzAAg3fV0");

        TestUtil.vertx().eventBus().send(EVENT_HA_CACHE_OBSERVER_SYNC, context);

        var counter = new AtomicInteger(0);

        TestUtil.vertx().setPeriodic(2 * 1000, timer ->
        {
            if (counter.get() == 3)
            {
                TestUtil.vertx().cancelTimer(timer);

                testContext.failNow("maximum attempts exceeded");
            }
            else
            {
                if (new File(CURRENT_DIR + PATH_SEPARATOR + CONFIG_DIR + PATH_SEPARATOR + "test-cache-file").exists())
                {
                    TestUtil.vertx().cancelTimer(timer);

                    testContext.completeNow();
                }

                counter.incrementAndGet();
            }
        });
    }
}