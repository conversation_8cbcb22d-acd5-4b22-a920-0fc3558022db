/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.provision;

import com.mindarray.*;
import com.mindarray.api.*;
import com.mindarray.db.ConfigDBConstants;
import com.mindarray.discovery.TestHCIObjectDiscovery;
import com.mindarray.discovery.TestSDNObjectDiscovery;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.nms.NMSConstants;
import com.mindarray.runbook.Runbook;
import com.mindarray.store.*;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import io.github.artsok.RepeatedIfExceptionsTest;
import io.vertx.core.Promise;
import io.vertx.core.eventbus.DeliveryOptions;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.junit5.Timeout;
import io.vertx.junit5.VertxExtension;
import io.vertx.junit5.VertxTestContext;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.TestAPIConstants.GROUP_API_ENDPOINT;
import static com.mindarray.TestAPIConstants.OBJECT_API_ENDPOINT;
import static com.mindarray.api.AIOpsObject.*;
import static com.mindarray.api.APIConstants.SESSION_ID;
import static com.mindarray.api.Group.*;
import static com.mindarray.eventbus.EventBusConstants.*;
import static org.apache.http.HttpStatus.SC_OK;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

@ExtendWith(VertxExtension.class)
@Timeout(80 * 1000)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@Execution(ExecutionMode.SAME_THREAD)
@EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY|SLAVE$")
public class TestObjectProvisionPostOperation
{
    private static final Logger LOGGER = new Logger(TestObjectProvision.class, MOTADATA_NMS, "Test Object Provision Post Operation");

    @AfterAll
    static void afterAll(VertxTestContext testContext)
    {
        TestHCIObjectDiscovery.messageConsumer.unregister(asyncResult -> TestSDNObjectDiscovery.messageConsumer.unregister(result ->
        {
            try
            {
                testContext.awaitCompletion(2, TimeUnit.SECONDS);

                testContext.completeNow();
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);

                testContext.failNow(exception);
            }
        }));

    }

    @BeforeEach
    void beforeEach(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running test case %s", testInfo.getTestMethod().get().getName()));

        testContext.completeNow();
    }

    //3667-bug
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(1)
    void testUpdateOffice365Object(VertxTestContext testContext, TestInfo testInfo) throws InterruptedException
    {
        testContext.awaitCompletion(2, TimeUnit.SECONDS);

        var id = ObjectConfigStore.getStore().getItemsByType(NMSConstants.Type.OFFICE_365).getLong(0);

        var parameter = ObjectConfigStore.getStore().getItem(id).put(AIOpsObject.OBJECT_CONTEXT, new JsonObject().put(NMSConstants.CLOUD_TENANT_ID, "fc4f4c2e-4ff3-4aaa-aa72-a8b487cfd5d6"));

        TestAPIUtil.put(OBJECT_API_ENDPOINT + "/" + id, parameter,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {

                            TestAPIUtil.assertUpdateEntityTestResult(ObjectConfigStore.getStore(), parameter, response.bodyAsJsonObject(),
                                    String.format(InfoMessageConstants.ENTITY_UPDATED, APIConstants.Entity.OBJECT.getName()), LOGGER, testInfo.getTestMethod().get().getName());

                            testContext.completeNow();

                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(3)
    void testGetItemsByTypes(VertxTestContext testContext)
    {
        var types = new JsonArray().add(NMSConstants.Type.WINDOWS.getName()).add(NMSConstants.Type.SWITCH.getName());

        var items = ObjectConfigStore.getStore().getItemsByTypes(types);

        for (var index = 0; index < items.size(); index++)
        {
            var item = ObjectConfigStore.getStore().getItem(items.getLong(index));

            Assertions.assertTrue(types.contains(item.getString(AIOpsObject.OBJECT_TYPE)));
        }

        testContext.completeNow();
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(4)
    void testGetItemsByCategories(VertxTestContext testContext)
    {
        var categories = new JsonArray().add(NMSConstants.Category.SERVER.getName()).add(NMSConstants.Category.NETWORK.getName());

        var items = ObjectConfigStore.getStore().getItemsByCategories(categories);

        for (var index = 0; index < items.size(); index++)
        {
            var item = ObjectConfigStore.getStore().getItem(items.getLong(index));

            Assertions.assertTrue(categories.contains(item.getString(AIOpsObject.OBJECT_CATEGORY)));
        }

        testContext.completeNow();
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(5)
    void testGetItemsByGroup(VertxTestContext testContext)
    {
        var id = ObjectConfigStore.getStore().getItemByIP("**********");

        var items = ObjectConfigStore.getStore().getItemsByGroup(ObjectConfigStore.getStore().getItem(id).getJsonArray(AIOpsObject.OBJECT_GROUPS).getLong(0));

        Assertions.assertTrue(items.contains(id));

        testContext.completeNow();
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(6)
    void testCheckLinuxObjectDisableStatus(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = ObjectConfigStore.getStore().getItemByIP("************", NMSConstants.Type.LINUX);

        var currentTimestamp = ObjectStatusCacheStore.getStore().getTimestamp(context.getLong(ID));

        var metricContext = MetricConfigStore.getStore().getItem(MetricConfigStore.getStore().getItemByMetricPlugin(context.getLong("id"), NMSConstants.Type.LINUX.getName()));

        metricContext.put(Metric.METRIC_POLLING_TIME, 1);

        TestAPIUtil.put(TestAPIConstants.METRIC_API_ENDPOINT + "/" + context.getLong(ID),
                new JsonObject().put(APIConstants.REQUEST_PARAMS, new JsonArray().add(metricContext)),
                testContext.succeeding(response -> testContext.verify(() ->
                        {
                            TestAPIUtil.assertValidResponseTestResult(response, LOGGER, testInfo.getTestMethod().get().getName());

                            var promise = Promise.<Void>promise();

                            TestUtil.vertx().eventBus().<JsonObject>request(EventBusConstants.EVENT_OBJECT_DISABLE, context, reply ->
                            {
                                if (reply.succeeded())
                                {
                                    assertEquals("object " + context.getString(OBJECT_NAME) + " disabled successfully...", reply.result().body().getString(MESSAGE));

                                    promise.complete();
                                }
                                else
                                {
                                    promise.fail(reply.cause());

                                    testContext.failNow(reply.cause());
                                }
                            });

                            promise.future().onComplete(result ->
                            {
                                try
                                {
                                    testContext.awaitCompletion(2, TimeUnit.SECONDS);

                                    assertTrue(ObjectStatusCacheStore.getStore().getItem(context.getLong(ID)).equalsIgnoreCase(NMSConstants.State.DISABLE.name()));

                                    assertTrue(ObjectStatusCacheStore.getStore().getTimestamp(context.getLong(ID)) > currentTimestamp);

                                    TestNMSUtil.assertEnableObjectTestResult(context, metricContext, testContext, testInfo.getTestMethod().get().getName());
                                }
                                catch (Exception ignore)
                                {
                                }
                            });
                        })
                ));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(7)
    void testCheckLinuxObjectMaintenanceStatus(VertxTestContext testContext, TestInfo testInfo) throws InterruptedException
    {
        testContext.awaitCompletion(2, TimeUnit.SECONDS);

        var context = ObjectConfigStore.getStore().getItemByIP("************", NMSConstants.Type.LINUX);

        var currentTimestamp = ObjectStatusCacheStore.getStore().getTimestamp(context.getLong(ID));

        var metricContext = MetricConfigStore.getStore().getItem(MetricConfigStore.getStore().getItemByMetricPlugin(context.getLong("id"), NMSConstants.Type.LINUX.getName()));

        metricContext.put(Metric.METRIC_POLLING_TIME, 1);

        TestAPIUtil.put(TestAPIConstants.METRIC_API_ENDPOINT + "/" + context.getLong(ID),
                new JsonObject().put(APIConstants.REQUEST_PARAMS, new JsonArray().add(metricContext)),
                testContext.succeeding(response -> testContext.verify(() ->
                        {
                            TestAPIUtil.assertValidResponseTestResult(response, LOGGER, testInfo.getTestMethod().get().getName());

                            var promise = Promise.<Void>promise();

                            TestUtil.vertx().eventBus().<JsonObject>request(EVENT_OBJECT_MAINTENANCE, context, reply ->
                            {
                                if (reply.succeeded())
                                {
                                    assertEquals("object " + context.getString(OBJECT_NAME) + " entered into maintenance state successfully...", reply.result().body().getString(MESSAGE));

                                    promise.complete();
                                }
                                else
                                {
                                    promise.fail(reply.cause());

                                    testContext.failNow(reply.cause());
                                }
                            });

                            promise.future().onComplete(result ->
                            {

                                try
                                {
                                    testContext.awaitCompletion(2, TimeUnit.SECONDS);

                                    assertTrue(ObjectStatusCacheStore.getStore().getItem(context.getLong(ID)).equalsIgnoreCase(NMSConstants.State.MAINTENANCE.name()));

                                    assertTrue(ObjectStatusCacheStore.getStore().getTimestamp(context.getLong(ID)) > currentTimestamp);

                                    TestNMSUtil.assertEnableObjectTestResult(context, metricContext, testContext, testInfo.getTestMethod().get().getName());
                                }
                                catch (Exception ignore)
                                {
                                }
                            });
                        })
                ));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(8)
    void testCheckServiceCheckObjectDisableStatus(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = ObjectConfigStore.getStore().getItemByIP("************", NMSConstants.Type.PING);

        var currentTimestamp = ObjectStatusCacheStore.getStore().getTimestamp(context.getLong(ID));

        var metricContext = MetricConfigStore.getStore().getItem(MetricConfigStore.getStore().getItemByMetricPlugin(context.getLong("id"), NMSConstants.Type.PING.getName()));

        LOGGER.debug("current timestamp : " + ObjectStatusCacheStore.getStore().getTimestamp(context.getLong(ID)));

        metricContext.put(Metric.METRIC_POLLING_TIME, 1);

        TestAPIUtil.put(TestAPIConstants.METRIC_API_ENDPOINT + "/" + context.getLong(ID),
                new JsonObject().put(APIConstants.REQUEST_PARAMS, new JsonArray().add(metricContext)),
                testContext.succeeding(response -> testContext.verify(() ->
                        {
                            LOGGER.debug("Response from Metric API Endpoint : " + response.bodyAsJsonObject());

                            TestAPIUtil.assertValidResponseTestResult(response, LOGGER, testInfo.getTestMethod().get().getName());

                            var promise = Promise.<Void>promise();

                            TestUtil.vertx().eventBus().<JsonObject>request(EventBusConstants.EVENT_OBJECT_DISABLE, context, reply ->
                            {
                                if (reply.succeeded())
                                {
                                    LOGGER.info(String.format("Object %s disabled successfully ", context.getString(OBJECT_IP, AIOpsObject.OBJECT_TARGET)));

                                    assertEquals("object " + context.getString(OBJECT_NAME) + " disabled successfully...", reply.result().body().getString(MESSAGE));

                                    promise.complete();
                                }
                                else
                                {
                                    promise.fail(reply.cause());

                                    testContext.failNow(reply.cause());
                                }
                            });

                            promise.future().onComplete(result ->
                            {

                                try
                                {
                                    testContext.awaitCompletion(2, TimeUnit.SECONDS);

                                    LOGGER.debug("Timestamp after getting response : " + ObjectStatusCacheStore.getStore().getTimestamp(context.getLong(ID)));

                                    assertTrue(ObjectStatusCacheStore.getStore().getItem(context.getLong(ID)).equalsIgnoreCase(NMSConstants.State.DISABLE.name()));

                                    assertTrue(ObjectStatusCacheStore.getStore().getTimestamp(context.getLong(ID)) > currentTimestamp);

                                    TestNMSUtil.assertEnableObjectTestResult(context, metricContext, testContext, testInfo.getTestMethod().get().getName());
                                }
                                catch (Exception ignore)
                                {
                                }
                            });
                        })
                ));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(9)
    void testCheckServiceCheckObjectMaintenanceStatus(VertxTestContext testContext, TestInfo testInfo) throws InterruptedException
    {
        testContext.awaitCompletion(2, TimeUnit.SECONDS);

        var context = ObjectConfigStore.getStore().getItemByIP("************", NMSConstants.Category.SERVICE_CHECK);

        var currentTimestamp = ObjectStatusCacheStore.getStore().getTimestamp(context.getLong(ID));

        var metricContext = MetricConfigStore.getStore().getItem(MetricConfigStore.getStore().getItemByMetricPlugin(context.getLong("id"), NMSConstants.Type.PING.getName()));

        LOGGER.debug("current timestamp : " + ObjectStatusCacheStore.getStore().getTimestamp(context.getLong(ID)));

        metricContext.put(Metric.METRIC_POLLING_TIME, 1);

        TestAPIUtil.put(TestAPIConstants.METRIC_API_ENDPOINT + "/" + context.getLong(ID),
                new JsonObject().put(APIConstants.REQUEST_PARAMS, new JsonArray().add(metricContext)),
                testContext.succeeding(response -> testContext.verify(() ->
                        {
                            LOGGER.debug("Response from Metric API Endpoint : " + response.bodyAsJsonObject());

                            TestAPIUtil.assertValidResponseTestResult(response, LOGGER, testInfo.getTestMethod().get().getName());

                            var promise = Promise.<Void>promise();

                            TestUtil.vertx().eventBus().<JsonObject>request(EventBusConstants.EVENT_OBJECT_MAINTENANCE, context, reply ->
                            {
                                if (reply.succeeded())
                                {
                                    LOGGER.info(String.format("Object %s gone to maintenance state ", context.getString(OBJECT_IP, AIOpsObject.OBJECT_TARGET)));

                                    assertEquals("object " + context.getString(OBJECT_NAME) + " entered into maintenance state successfully...", reply.result().body().getString(MESSAGE));

                                    promise.complete();
                                }
                                else
                                {
                                    promise.fail(reply.cause());

                                    testContext.failNow(reply.cause());
                                }
                            });

                            promise.future().onComplete(result ->
                            {

                                try
                                {
                                    testContext.awaitCompletion(2, TimeUnit.SECONDS);

                                    LOGGER.debug("Timestamp after getting response : " + ObjectStatusCacheStore.getStore().getTimestamp(context.getLong(ID)));

                                    assertTrue(ObjectStatusCacheStore.getStore().getItem(context.getLong(ID)).equalsIgnoreCase(NMSConstants.State.MAINTENANCE.name()));

                                    assertTrue(ObjectStatusCacheStore.getStore().getTimestamp(context.getLong(ID)) > currentTimestamp);

                                    TestNMSUtil.assertEnableObjectTestResult(context, metricContext, testContext, testInfo.getTestMethod().get().getName());
                                }
                                catch (Exception ignore)
                                {
                                }
                            });
                        })
                ));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(10)
    void testRootLinuxFailedDiscovery(VertxTestContext testContext)
    {
        var context = DiscoveryConfigStore.getStore().getItemByValue(Discovery.DISCOVERY_NAME, "ssh-************");

        Assertions.assertNotNull(context);

        TestAPIUtil.post(String.format(TestAPIConstants.DISCOVERY_RUN_API_ENDPOINT, context.getLong(ID)), new JsonObject(), response -> testContext.verify(() ->
        {

            assertEquals(SC_OK, response.result().statusCode());

            testContext.completeNow();
        }));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(11)
    void testPingServiceCheckObjectDiscovery(VertxTestContext testContext)
    {
        var discoveryContext = new JsonObject("{\"discovery.category\":\"Service Check\",\"discovery.type\":\"ip.address\",\"discovery.object.type\":\"Ping\",\"discovery.name\":\"Ping Object\",\"discovery.event.processors\":[],\"discovery.groups\":[10000000000013],\"discovery.method\":\"REMOTE\",\"ping.check.status\":\"no\",\"port.check.status\":\"no\",\"discovery.target.type\":\"OBJECT\",\"timeout\":60,\"discovery.context\":{\"snmp.check.retries\":1}}");

        var promise = Promise.promise();

        discoveryContext.put(Discovery.DISCOVERY_TARGET, ObjectConfigStore.getStore().getItemByValue(Discovery.DISCOVERY_NAME, "Oracle-************").getLong(ID));

        if (discoveryContext.containsKey("discovery.credential.profile.context"))
        {
            var item = CredentialProfileConfigStore.getStore().getItemByValue(CredentialProfile.CREDENTIAL_PROFILE_NAME, discoveryContext.getJsonObject("discovery.credential.profile.context").getString(CredentialProfile.CREDENTIAL_PROFILE_NAME));

            discoveryContext.put(Discovery.DISCOVERY_CREDENTIAL_PROFILES, new JsonArray().add(item.getLong(ID)));
        }
        else
        {
            discoveryContext.put(Discovery.DISCOVERY_CREDENTIAL_PROFILES, new JsonArray());
        }

        discoveryContext.put(Discovery.DISCOVERY_GROUPS, new JsonArray().add(CommonUtil.getLong("10000000000013")));

        discoveryContext.put(Discovery.DISCOVERY_EVENT_PROCESSORS, new JsonArray()
                .add(RemoteEventProcessorConfigStore.getStore().getItemByValue(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID, Bootstrap.getRegistrationId())
                        .getLong(ID)));

        TestAPIUtil.post(TestAPIConstants.DISCOVERY_API_ENDPOINT, discoveryContext, testContext.succeeding(discoveryResponse -> testContext.verify(() ->
        {
            assertEquals(SC_OK, discoveryResponse.statusCode());

            promise.complete(discoveryResponse.bodyAsJsonObject().getLong(ID));
        })));

        promise.future().onComplete(result ->
        {

            if (result.succeeded())
            {
                TestAPIUtil.post(String.format(TestAPIConstants.DISCOVERY_RUN_API_ENDPOINT, result.result()), new JsonObject(), response -> testContext.verify(() -> assertEquals(SC_OK, response.result().statusCode())));

                testContext.completeNow();
            }
            else
            {
                LOGGER.error(result.cause().getCause());

                testContext.failNow(result.cause());
            }
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(12)
    void testCheckGroupANDOperator(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = new JsonObject()
                .put(FIELD_GROUP_NAME, "groupTestNetworkAnd1").put(FIELD_PARENT_GROUP, 0)
                .put(GROUP_CONTEXT, new JsonObject().put(GROUP_AUTO_ASSIGN, YES)).put(GROUP_OPERATOR, "and").put(OBJECT_VENDOR, new JsonArray().add("Cisco Systems"));

        TestAPIUtil.post(GROUP_API_ENDPOINT, context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertCreateEntityTestResult(GroupConfigStore.getStore(), context, response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_CREATED, APIConstants.Entity.GROUP.getName()), null, LOGGER, testInfo.getTestMethod().get().getName());

                            for (var group : ObjectConfigStore.getStore().getItemByIP("**********", NMSConstants.Type.SWITCH).getJsonArray(AIOpsObject.OBJECT_GROUPS))
                            {
                                if (group.equals(response.bodyAsJsonObject().getLong(ID)))
                                {
                                    testContext.completeNow();
                                }

                            }
                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(13)
    void testValidateMACAddressNutanixType(VertxTestContext testContext)
    {
        try
        {
            var objects = ObjectConfigStore.getStore().getItemsByType(NMSConstants.Type.NUTANIX);

            Assertions.assertNotNull(objects);

            var objectIP = ObjectConfigStore.getStore().getItem(CommonUtil.getLong(objects.getLong(0))).getString(OBJECT_IP);

            LOGGER.info(String.format("device IP address : %s ", objectIP));

            var items = MACScannerConfigStore.getStore().getItems();

            var attempts = new AtomicLong();

            TestUtil.vertx().setPeriodic(5 * 1000L, timer ->
            {
                var valid = false;

                for (var index = 0; index < items.size(); index++)
                {
                    if (items.getJsonObject(index).getString(MACScanner.MAC_SCANNER_DEVICE_IP_ADDRESS).equalsIgnoreCase(objectIP))
                    {
                        TestUtil.vertx().cancelTimer(timer);

                        valid = true;

                        break;
                    }
                }

                attempts.getAndIncrement();

                if (valid)
                {
                    testContext.completeNow();
                }
                else
                {
                    if (attempts.get() >= 5)
                    {
                        testContext.failNow("MAC Address not found ");
                    }
                }

            });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(14)
    void testDiscoveryRerunHCICategory(VertxTestContext testContext)
    {
        var item = DiscoveryConfigStore.getStore().getItemByValue(Discovery.DISCOVERY_OBJECT_TYPE, NMSConstants.Type.PRISM.getName());

        Assertions.assertNotNull(item);

        TestHCIObjectDiscovery.DISCOVERY_ITEMS.clear();

        TestHCIObjectDiscovery.assertDiscoveryConsumerSetup();

        TestHCIObjectDiscovery.runHCIDiscoveryTest(testContext, item);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(15)
    void testDiscoveryRerunSDNCategory(VertxTestContext testContext)
    {
        var item = DiscoveryConfigStore.getStore().getItemByValue(Discovery.DISCOVERY_OBJECT_TYPE, NMSConstants.Type.CISCO_VMANAGE.getName());

        Assertions.assertNotNull(item);

        TestSDNObjectDiscovery.DISCOVERY_ITEMS.clear();

        TestSDNObjectDiscovery.assertDiscoveryConsumerSetup();

        TestSDNObjectDiscovery.runSDNDiscoveryTest(testContext, item);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(16)
    void testVerifyDuplicateObjectProvision(VertxTestContext testContext)
    {
        var item = DiscoveryConfigStore.getStore().getItemByValue(Discovery.DISCOVERY_OBJECT_TYPE, NMSConstants.Type.PRISM.getName());

        Assertions.assertNotNull(item);

        Bootstrap.configDBService().getAll(ConfigDBConstants.COLLECTION_DISCOVERY_RESULT + item.getLong(ID), result ->
        {
            if (result.succeeded())
            {
                Assertions.assertFalse(result.result().isEmpty());

                var probe = result.result().getJsonObject(0);

                Assertions.assertNotNull(probe);

                probe.mergeIn(item).put(SESSION_ID, TestUtil.getSessionId());

                TestUtil.vertx().eventBus().<JsonObject>request(EVENT_OBJECT_PROVISION, probe.put(EVENT_REPLY, YES),
                        new DeliveryOptions().setSendTimeout(300000L), reply ->
                        {
                            try
                            {
                                if (reply.failed())
                                {
                                    LOGGER.error(reply.cause());

                                    testContext.failNow(reply.cause());
                                }
                                else
                                {
                                    var response = reply.result().body();

                                    Assertions.assertNotNull(response);

                                    Assertions.assertTrue(response.containsKey(STATUS));

                                    Assertions.assertTrue(response.containsKey(MESSAGE));

                                    Assertions.assertEquals(STATUS_FAIL, response.getString(STATUS));

                                    Assertions.assertTrue(response.getString(MESSAGE).contains("already provisioned"));

                                    testContext.completeNow();
                                }
                            }
                            catch (Exception exception)
                            {
                                LOGGER.error(exception);
                            }
                        });
            }
            else
            {
                LOGGER.error(result.cause());

                testContext.failNow(result.cause());
            }
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(17)
    void testCheckAssignedDefaultRunbooks(VertxTestContext testContext)
    {
        var item = ObjectConfigStore.getStore().getItem(ObjectConfigStore.getStore().getItemByIP("fd00:1:1:1::47"));

        LOGGER.info("item : " + item.encode());

        var runbooks = RunbookPluginConfigStore.getStore().getItems(Runbook.DEFAULT_RUNBOOKS);

        LOGGER.info("runbook : " + runbooks.encode());

        for (var index = 0; index < runbooks.size(); index++)
        {
            var context = runbooks.getJsonObject(index);

            Assertions.assertTrue(context.getJsonArray(RunbookPlugin.RUNBOOK_PLUGIN_ENTITIES).contains(item.getLong(ID)));
        }

        testContext.completeNow();
    }
}