/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.streaming;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.TestNMSUtil;
import com.mindarray.TestUtil;
import com.mindarray.api.AIOpsObject;
import com.mindarray.api.Metric;
import com.mindarray.db.ConfigDBConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.nms.NMSConstants;
import com.mindarray.store.MetricCacheStore;
import com.mindarray.store.MetricConfigStore;
import com.mindarray.store.ObjectConfigStore;
import com.mindarray.util.CodecUtil;
import com.mindarray.util.CommonUtil;
import io.github.artsok.RepeatedIfExceptionsTest;
import io.vertx.core.eventbus.MessageConsumer;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.junit5.Timeout;
import io.vertx.junit5.VertxExtension;
import io.vertx.junit5.VertxTestContext;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.extension.ExtendWith;

import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.eventbus.EventBusConstants.*;
import static com.mindarray.streaming.StreamingEngine.STREAMING_TYPE;
import static org.junit.jupiter.api.Assertions.assertEquals;

@ExtendWith(VertxExtension.class)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@Timeout(70 * 1000)
@EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY$")
public
class TestEventTracker
{
    private static final JsonObject CONTEXT = new JsonObject();
    private static MessageConsumer<JsonObject> messageConsumer;
    private static String eventStatus;

    @BeforeAll
    static void beforeAll(VertxTestContext testContext)
    {
        TestUtil.startEventStreaming(new JsonObject());

        var objects = ObjectConfigStore.getStore().getItems();

        Assertions.assertTrue(objects != null && !objects.isEmpty());

        for (var index = 0; index < objects.size(); index++)
        {
            if (objects.getJsonObject(index) != null && objects.getJsonObject(index).getString(AIOpsObject.OBJECT_TYPE) != null)
            {
                CONTEXT.put(objects.getJsonObject(index).getString(AIOpsObject.OBJECT_TYPE), objects.getJsonObject(index).getLong(ID));
            }
        }

        var metricIds = new JsonArray(MetricConfigStore.getStore().getItems().stream().map(item -> JsonObject.mapFrom(item).getLong(GlobalConstants.ID)).collect(Collectors.toList()));

        for (var index = 0; index < metricIds.size(); index++)
        {
            MetricCacheStore.getStore().deleteMetric(metricIds.getLong(index));
        }

        Bootstrap.configDBService().updateAll(ConfigDBConstants.COLLECTION_METRIC,
                new JsonObject().put(ConfigDBConstants.FIELD_NAME, GlobalConstants.ID).put(VALUE, metricIds),
                new JsonObject().put(Metric.METRIC_STATE, NMSConstants.State.DISABLE.name()),
                GlobalConstants.DEFAULT_USER, GlobalConstants.SYSTEM_REMOTE_ADDRESS,
                result -> MetricConfigStore.getStore().updateItems(metricIds).onComplete(asyncResult -> testContext.completeNow()));


    }

    @AfterAll
    static void afterAll(VertxTestContext testContext)
    {
        if (messageConsumer != null)
        {
            messageConsumer.unregister(result -> testContext.completeNow());
        }
        else
        {
            testContext.completeNow();
        }
    }

    private static void assertEventTrackerEventNameTestResult(VertxTestContext testContext, JsonObject metric)
    {
        try
        {
            Bootstrap.vertx().eventBus().send(EVENT_ROUTER, metric);

            messageConsumer = Bootstrap.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
            {
                if (message.body() != null && message.body().getString(EventBusConstants.EVENT_TYPE).equalsIgnoreCase(EventBusConstants.UI_NOTIFICATION_STREAMING_BROADCAST))
                {
                    try
                    {
                        var eventContext = CodecUtil.toJSONObject(message.body().getBinary(EventBusConstants.EVENT_CONTEXT));

                        if (!eventContext.isEmpty() && eventContext.getString(STREAMING_TYPE).equalsIgnoreCase(StreamingEngine.StreamingType.EVENT_TRACKER.getName()) && eventContext.getString(EVENT_STATE) != null && eventContext.getString(EVENT_STATE).equals(EVENT_STATE_COMPLETED)
                                && eventContext.containsKey(EVENT_ID) && eventContext.getLong(EVENT_ID).equals(metric.getLong(EVENT_ID)))
                        {
                            assertEquals("Monitor Poll", eventContext.getString(EVENT_NAME));

                            assertEquals(eventContext.getString(STATUS), eventStatus != null ? eventStatus : STATUS_SUCCEED);

                            messageConsumer.unregister(result -> testContext.completeNow());
                        }
                    }
                    catch (Exception exception)
                    {
                        messageConsumer.unregister(result -> testContext.failNow(exception));
                    }
                }

            });
        }
        catch (Exception exception)
        {
            messageConsumer.unregister(result -> testContext.failNow(exception));
        }
    }

    private static void assertEventTrackerTestResult(VertxTestContext testContext, String eventState, JsonObject metric, long eventId)
    {
        try
        {
            messageConsumer = Bootstrap.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
            {
                if (message.body() != null && message.body().getString(EventBusConstants.EVENT_TYPE).equalsIgnoreCase(EventBusConstants.UI_NOTIFICATION_STREAMING_BROADCAST))
                {
                    try
                    {
                        var eventContext = CodecUtil.toJSONObject(message.body().getBinary(EventBusConstants.EVENT_CONTEXT));

                        if (!eventContext.isEmpty() && eventContext.getString(STREAMING_TYPE).equalsIgnoreCase(StreamingEngine.StreamingType.EVENT_TRACKER.getName()) && eventContext.getString(EVENT_STATE) != null)
                        {
                            if (eventContext.getString(EVENT_STATE).equals(EVENT_STATE_COMPLETED)
                                    && eventContext.containsKey(EVENT_ID) && eventContext.getLong(EVENT_ID).equals(eventId))
                            {
                                messageConsumer.unregister();

                                assertEquals(eventContext.getString(STATUS), eventStatus != null ? eventStatus : STATUS_SUCCEED);

                                testContext.completeNow();
                            }
                            else if (eventState.equals(eventContext.getString(EVENT_STATE)))
                            {
                                assertEquals(eventState, eventContext.getString(EVENT_STATE));
                            }
                        }
                    }
                    catch (Exception exception)
                    {
                        messageConsumer.unregister(result -> testContext.failNow(exception));
                    }
                }

            });

            /*TestUtil.vertx().eventBus().send(EventConstants.EVENT_ADD,new JsonObject().put(EventConstants.EVENT_ID,eventId)
                    .put(EventConstants.EVENT_TYPE, EventConstants.EVENT_METRIC_POLL)
                    .put(EventConstants.EVENT_STATE,EventConstants.EVENT_STATE_QUEUED)
                    .put(User.USER_NAME, SYSTEM_USER)
                    .put(EventConstants.EVENT_CONTEXT,metric));

            metric.put(EventConstants.EVENT_ID,eventId);

            EventConstants.updateEvent(eventId, String.format(InfoMessageConstants.EVENT_TRACKER_EVENT_DISPATCHED, Bootstrap.getRegistrationId(), DateTimeUtil.timestamp()));*/

            Bootstrap.vertx().eventBus().send(EVENT_ROUTER, metric.put(ID, CommonUtil.newId()));
        }
        catch (Exception exception)
        {
            messageConsumer.unregister(result -> testContext.failNow(exception));
        }
    }

    @BeforeEach
    void beforeEach(VertxTestContext testContext)
    {
        if (messageConsumer != null)
        {
            messageConsumer.unregister(result -> testContext.completeNow());
        }
        else
        {
            testContext.completeNow();
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(1)
    void testEventStateRunning(VertxTestContext testContext)
    {
        eventStatus = STATUS_SUCCEED;

        var metric = TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByValue(AIOpsObject.OBJECT_IP, "************").getLong(ID))
                .put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.AVAILABILITY.getName()), testContext);

        Assertions.assertNotNull(metric);

        assertEventTrackerTestResult(testContext, EVENT_STATE_RUNNING, metric.put(GlobalConstants.TIMEOUT, 60), metric.getLong(EVENT_ID));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(2)
    void testEventStatusQueued(VertxTestContext testContext) throws Exception
    {
        testContext.awaitCompletion(2, TimeUnit.SECONDS);

        eventStatus = STATUS_SUCCEED;

        var metric = TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByValue(AIOpsObject.OBJECT_IP, "************").getLong(ID))
                .put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.AVAILABILITY.getName()), testContext);

        Assertions.assertNotNull(metric);

        assertEventTrackerTestResult(testContext, EVENT_STATE_QUEUED, metric.put(GlobalConstants.TIMEOUT, 60), metric.getLong(EVENT_ID));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(3)
    void testEventStateCompleted(VertxTestContext testContext) throws Exception
    {
        testContext.awaitCompletion(1, TimeUnit.SECONDS);

        eventStatus = STATUS_SUCCEED;

        var metric = TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByValue(AIOpsObject.OBJECT_IP, "**********").getLong(ID))
                .put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.AVAILABILITY.getName()), testContext);

        Assertions.assertNotNull(metric);

        assertEventTrackerTestResult(testContext, EVENT_STATE_COMPLETED, metric.put(GlobalConstants.TIMEOUT, 60), metric.getLong(EVENT_ID));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(4)
    void testFilterEventByTypeMetricPoll(VertxTestContext testContext) throws Exception
    {
        testContext.awaitCompletion(2, TimeUnit.SECONDS);

        var metric = TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByValue(AIOpsObject.OBJECT_IP, "**********").getLong(ID))
                .put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.AVAILABILITY.getName()), testContext);

        assertEventTrackerEventNameTestResult(testContext, metric.put(GlobalConstants.TIMEOUT, 60));
    }

}
