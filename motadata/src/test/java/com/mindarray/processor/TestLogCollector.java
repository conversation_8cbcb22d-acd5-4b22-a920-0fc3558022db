/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.processor;

import com.mindarray.Bootstrap;
import com.mindarray.TestAPIUtil;
import com.mindarray.TestUtil;
import com.mindarray.datastore.DatastoreConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.runbook.RunbookEngine;
import com.mindarray.store.LogCollectorConfigStore;
import com.mindarray.util.Logger;
import io.github.artsok.RepeatedIfExceptionsTest;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.json.JsonObject;
import io.vertx.junit5.Timeout;
import io.vertx.junit5.VertxExtension;
import io.vertx.junit5.VertxTestContext;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.TestAPIConstants.LOG_COLLECTOR_API_ENDPOINT;
import static com.mindarray.TestAPIConstants.RUNBOOK_PLUGIN_API_ENDPOINT;
import static com.mindarray.api.APIConstants.RESPONSE_CODE;
import static com.mindarray.api.LogCollector.LOG_COLLECTOR_INTERVAL;
import static com.mindarray.api.LogCollector.LOG_COLLECTOR_RUNBOOK;
import static org.apache.http.HttpStatus.SC_OK;

@ExtendWith(VertxExtension.class)
@Execution(ExecutionMode.SAME_THREAD)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@Timeout(70 * 1000)
@EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY|SLAVE$")
public class TestLogCollector
{

    private static final Logger LOGGER = new Logger(TestLogCollector.class, MOTADATA_LOG, "Test Log Collector");
    private static final String EVENT_SOURCE = "***********";
    private static Long LOG_RUNBOOK_ID = 0L;
    private static Long LOG_COLLECTOR_ID = 0L;

    @BeforeAll
    static void beforeAll(VertxTestContext testContext)
    {
        var deploymentId = Bootstrap.getDeployedVerticles().get(RunbookEngine.class.getSimpleName());

        Bootstrap.vertx().undeploy(deploymentId, asyncResult ->
        {
            Bootstrap.vertx().eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_RUNBOOK, message ->
            {
                var event = message.body();

                Assertions.assertFalse(event.isEmpty());

                TestUtil.vertx().createDatagramSocket().send(EVENT_SOURCE + "    sending some logs...", 5142, "localhost");

                message.reply(event);
            });

            testContext.completeNow();

        });
    }

    @AfterAll
    static void afterAll(VertxTestContext testContext)
    {
        Bootstrap.vertx().deployVerticle(new RunbookEngine(), asyncResult ->
        {
            if (asyncResult.succeeded())
            {
                testContext.completeNow();
            }
            else
            {
                testContext.failNow(asyncResult.cause());
            }
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(1)
    @Timeout(5000)
    void testCreateLogRunbook(VertxTestContext testContext, TestInfo testInfo) throws InterruptedException
    {
        LOGGER.info(String.format("running test case : %s", testInfo.getTestMethod().get().getName()));

        var payload = new JsonObject("{\"runbook.plugin.name\":\"testLogCollection1\",\"runbook.plugin.type\":\"Custom Script\",\"runbook.plugin.entity.type\":\"event.source\",\"runbook.plugin.category\":\"other\",\"runbook.plugin.entities\":[\"***********\"],\"runbook.plugin.context\":{\"runbook.plugin.credential.profile\":55158940587,\"timeout\":160,\"script\":\"package main\\n\\nimport (\\n\\t\\\"encoding/base64\\\"\\n\\t\\\"encoding/json\\\"\\n\\t\\\"fmt\\\"\\n\\t\\\"motadatasdk/consts\\\"\\n\\t\\\"motadatasdk/motadatatypes\\\"\\n\\t\\\"motadatasdk/utils\\\"\\n\\t\\\"net\\\"\\n\\t\\\"os\\\"\\n\\t\\\"strconv\\\"\\n)\\n\\nfunc main() {\\n\\n\\tcontext, err := utils.LoadPluginContext(os.Args[2:][0])\\n\\n\\tif err != nil {\\n\\n\\t\\tbytes, _ := json.Marshal(motadatatypes.MotadataMap{\\n\\n\\t\\t\\tconsts.Status: consts.StatusFail,\\n\\n\\t\\t\\tconsts.Errors: []motadatatypes.MotadataStringMap{\\n\\t\\t\\t\\t{\\n\\t\\t\\t\\t\\tconsts.ErrorCode: consts.ErrorCodeInternalError,\\n\\t\\t\\t\\t\\tconsts.Error:     fmt.Sprintf(\\\"%v\\\", err),\\n\\t\\t\\t\\t\\tconsts.Message:   \\\"Failed to load context\\\",\\n\\t\\t\\t\\t}},\\n\\t\\t})\\n\\n\\t\\tfmt.Println(base64.StdEncoding.EncodeToString(bytes) + consts.BlankString)\\n\\n\\t} else {\\n\\n\\t\\tresult := make(motadatatypes.MotadataMap)\\n\\n\\t\\trun(result, context)\\n\\n\\t\\tbytes, err := json.Marshal(result)\\n\\n\\t\\tif err != nil {\\n\\n\\t\\t\\tbytes, _ = json.Marshal(motadatatypes.MotadataMap{\\n\\n\\t\\t\\t\\tconsts.Status: consts.StatusFail,\\n\\n\\t\\t\\t\\tconsts.Errors: []motadatatypes.MotadataStringMap{\\n\\t\\t\\t\\t\\t{\\n\\t\\t\\t\\t\\t\\tconsts.ErrorCode: consts.ErrorCodeInternalError,\\n\\t\\t\\t\\t\\t\\tconsts.Error:     fmt.Sprintf(\\\"%v\\\", err),\\n\\t\\t\\t\\t\\t\\tconsts.Message:   \\\"Invalid Result\\\",\\n\\t\\t\\t\\t\\t}},\\n\\t\\t\\t})\\n\\t\\t}\\n\\n\\t\\tfmt.Println(base64.StdEncoding.EncodeToString(bytes) + consts.BlankString)\\n\\t}\\n}\\n\\nfunc run(result motadatatypes.MotadataMap, context motadatatypes.MotadataMap) {\\n\\t\\n\\tresult[consts.Status] = consts.StatusFail\\n\\n\\tport := 515 // Default UDP log listener port\\n\\n\\teventSource := \\\"***********\\\"\\n\\t\\n\\tsocket, _ := net.Dial(\\\"udp\\\", \\\"localhost:\\\"+strconv.Itoa(port))\\n\\n\\t_, _ = socket.Write([]byte(fmt.Sprintf(\\\"%ssample log\\\", eventSource)))\\n\\t\\n\\tresult[consts.Status] = consts.StatusSucceed\\n}\\n\\n\\n\",\"script.language\":\"go\"}}");

        TestAPIUtil.post(RUNBOOK_PLUGIN_API_ENDPOINT, payload, testContext.succeeding(response -> testContext.verify(() ->
        {
            var body = response.bodyAsJsonObject();

            LOGGER.info(String.format("API response : %s ", body.encode()));

            Assertions.assertNotNull(response);

            Assertions.assertEquals(STATUS_SUCCEED, body.getString(STATUS));

            Assertions.assertEquals(SC_OK, body.getInteger(RESPONSE_CODE));

            LOG_RUNBOOK_ID = body.getLong(ID);

            testContext.completeNow();
        })));

        Assertions.assertTrue(testContext.awaitCompletion(60, TimeUnit.SECONDS), "could not complete the task, reason: timed out.");
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(2)
    @Timeout(5000)
    void testCreateLogCollectorProfile(VertxTestContext testContext, TestInfo testInfo) throws InterruptedException
    {
        LOGGER.info(String.format("running test case : %s", testInfo.getTestMethod().get().getName()));

        TestUtil.vertx().eventBus().<byte[]>localConsumer(EventBusConstants.EVENT_DATASTORE_WRITE + "." + DatastoreConstants.DatastoreCategory.EVENT.getName(), message ->
        {
            try
            {
                var event = TestUtil.decodeEventBufferSingleRow(Buffer.buffer(message.body()));

                if (event.getString(PLUGIN_ID).equalsIgnoreCase(DatastoreConstants.PluginId.UNKNOWN_LOG_EVENT.getName() + "-" + "other"))
                {
                    testContext.verify(() ->
                    {
                        Assertions.assertTrue(event.getString(MESSAGE).contains("sending some"));

                        Assertions.assertTrue(event.getString("source").contains(EVENT_SOURCE));

                        testContext.completeNow();

                    });
                }
            }
            catch (Exception exception)
            {
                testContext.failNow(exception);
            }
        });

        var payload = new JsonObject("{\"log.collector.name\":\"testLogCollectionProfile1\",\"log.collector.type\":\"ssh\",\"log.collector.log.parser\":[10000000000004],\"log.collector.runbook\":55158940606,\"log.collector.profile.state\":\"yes\",\"log.collector.interval\":10,\"log.collector.timeout\":60}").put(LOG_COLLECTOR_RUNBOOK, LOG_RUNBOOK_ID);

        TestAPIUtil.post(LOG_COLLECTOR_API_ENDPOINT, payload, testContext.succeeding(response -> testContext.verify(() ->
        {
            var body = response.bodyAsJsonObject();

            LOGGER.info(String.format("API response : %s ", body.encode()));

            Assertions.assertNotNull(response);

            Assertions.assertEquals(STATUS_SUCCEED, body.getString(STATUS));

            Assertions.assertEquals(SC_OK, body.getInteger(RESPONSE_CODE));

            LOG_COLLECTOR_ID = body.getLong(ID);

        })));

        Assertions.assertTrue(testContext.awaitCompletion(120, TimeUnit.SECONDS), "could not complete the task, reason: timed out.");

    }


    @RepeatedIfExceptionsTest(suspend = 20000L)
    @Order(3)
    @Timeout(5000)
    void testUpdateCollectorProfile(VertxTestContext testContext, TestInfo testInfo) throws InterruptedException
    {
        LOGGER.info(String.format("running test case : %s", testInfo.getTestMethod().get().getName()));

        var payload = LogCollectorConfigStore.getStore().getItem(LOG_COLLECTOR_ID);

        TestAPIUtil.put(LOG_COLLECTOR_API_ENDPOINT + "/" + LOG_COLLECTOR_ID, payload.put(LOG_COLLECTOR_INTERVAL, 120), testContext.succeeding(response -> testContext.verify(() ->
        {
            var body = response.bodyAsJsonObject();

            LOGGER.info(String.format("API response : %s ", body.encode()));

            Assertions.assertNotNull(response);

            Assertions.assertEquals(STATUS_SUCCEED, body.getString(STATUS));

            Assertions.assertEquals(SC_OK, body.getInteger(RESPONSE_CODE));

            var retries = new AtomicInteger(3);

            TestUtil.vertx().setPeriodic(3000, timer ->
            {
                var item = LogCollectorConfigStore.getStore().getItem(LOG_COLLECTOR_ID);

                if (item != null && item.getInteger(LOG_COLLECTOR_INTERVAL).equals(120))
                {
                    testContext.completeNow();
                }
                else
                {
                    if (retries.decrementAndGet() <= 0)
                    {
                        testContext.failNow("max attempt exceeded..event-source should be deregistered");
                    }
                }
            });
        })));

        Assertions.assertTrue(testContext.awaitCompletion(60, TimeUnit.SECONDS), "could not complete the task, reason: timed out.");
    }

    @RepeatedIfExceptionsTest(suspend = 20000L)
    @Order(4)
    @Timeout(5000)
    void testDeleteCollectorProfile(VertxTestContext testContext, TestInfo testInfo) throws InterruptedException
    {
        LOGGER.info(String.format("running test case : %s", testInfo.getTestMethod().get().getName()));

        TestAPIUtil.delete(LOG_COLLECTOR_API_ENDPOINT + "/" + LOG_COLLECTOR_ID, testContext.succeeding(response -> testContext.verify(() ->
        {
            var body = response.bodyAsJsonObject();

            LOGGER.info(String.format("API response : %s ", body.encode()));

            Assertions.assertNotNull(response);

            Assertions.assertEquals(STATUS_SUCCEED, body.getString(STATUS));

            Assertions.assertEquals(SC_OK, body.getInteger(RESPONSE_CODE));

            var retries = new AtomicInteger(3);

            TestUtil.vertx().setPeriodic(3000, timer ->
            {
                var item = LogCollectorConfigStore.getStore().getItem(LOG_COLLECTOR_ID);

                if (item == null)
                {
                    testContext.completeNow();
                }
                else
                {
                    if (retries.decrementAndGet() <= 0)
                    {
                        testContext.failNow("max attempt exceeded..event-source should be deregistered");
                    }
                }
            });
        })));

        Assertions.assertTrue(testContext.awaitCompletion(60, TimeUnit.SECONDS), "could not complete the task, reason: timed out.");
    }
}


