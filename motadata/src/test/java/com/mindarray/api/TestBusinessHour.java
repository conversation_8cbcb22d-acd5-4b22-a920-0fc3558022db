/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.api;

import com.mindarray.ErrorMessageConstants;
import com.mindarray.GlobalConstants;
import com.mindarray.InfoMessageConstants;
import com.mindarray.TestAPIUtil;
import com.mindarray.store.BusinessHourConfigStore;
import com.mindarray.util.Logger;
import io.github.artsok.RepeatedIfExceptionsTest;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.junit5.VertxExtension;
import io.vertx.junit5.VertxTestContext;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

import static com.mindarray.GlobalConstants.MOTADATA_API;
import static com.mindarray.GlobalConstants.RESULT;
import static com.mindarray.TestAPIConstants.BUSINESS_HOUR_API_ENDPOINT;
import static com.mindarray.api.APIConstants.RESPONSE_CODE;
import static com.mindarray.api.BusinessHour.BUSINESS_HOUR_CONTEXT;
import static com.mindarray.api.BusinessHour.BUSINESS_HOUR_NAME;
import static org.apache.http.HttpStatus.SC_OK;
import static org.junit.jupiter.api.Assertions.assertEquals;

@ExtendWith(VertxExtension.class)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@Execution(ExecutionMode.SAME_THREAD)
@EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY$")
public
class TestBusinessHour
{
    private static final String ENTITY_NAME = "Monitoring Hour";
    private static final JsonObject CONTEXT = new JsonObject().put(BUSINESS_HOUR_NAME, "Test")
            .put(BUSINESS_HOUR_CONTEXT, new JsonObject().put("Sunday", new JsonArray().add(9).add(1).add(2).add(3)));
    private static final Logger LOGGER = new Logger(TestBusinessHour.class, MOTADATA_API, "Test BusinessHour");
    private static long ID = 0L;

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(1)
    void testGetAllBusinessHour(VertxTestContext testContext)
    {
        TestAPIUtil.get(BUSINESS_HOUR_API_ENDPOINT
                , testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertGETAllRequestTestResult(response, BusinessHourConfigStore.getStore(), null);

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(2)
    void testCreateBusinessHour(VertxTestContext testContext, TestInfo testInfo)
    {
        TestAPIUtil.post(BUSINESS_HOUR_API_ENDPOINT, CONTEXT,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {

                            TestAPIUtil.assertCreateEntityTestResult(BusinessHourConfigStore.getStore(), CONTEXT, response.bodyAsJsonObject(),
                                    String.format(InfoMessageConstants.ENTITY_CREATED, ENTITY_NAME), null, LOGGER, testInfo.getTestMethod().get().getName());

                            ID = response.bodyAsJsonObject().getLong(GlobalConstants.ID);

                            testContext.completeNow();
                        })));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(3)
    void testCreateBusinessHourAlreadyExist(VertxTestContext testContext)
    {
        TestAPIUtil.post(BUSINESS_HOUR_API_ENDPOINT, CONTEXT,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertAlreadyExistedEntityTestResult(response.bodyAsJsonObject(), String.format(ErrorMessageConstants.API_FIELD_UNIQUE_RULE, ENTITY_NAME + " Name"),
                                    BusinessHourConfigStore.getStore(), BUSINESS_HOUR_NAME, CONTEXT.getString(BUSINESS_HOUR_NAME));

                            testContext.completeNow();
                        })));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(4)
    void testGetBusinessHour(VertxTestContext testContext, TestInfo testInfo)
    {
        TestAPIUtil.get(BUSINESS_HOUR_API_ENDPOINT + "/" + ID
                , testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertGETRequestTestResult(response, ID, BusinessHourConfigStore.getStore(), null, LOGGER, testInfo.getTestMethod().get().getName());

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(5)
    void testUpdateBusinessHour(VertxTestContext testContext, TestInfo testInfo)
    {

        var item = CONTEXT.put(BUSINESS_HOUR_NAME, "Test Update")
                .put(BUSINESS_HOUR_CONTEXT, new JsonObject().put("Monday", new JsonArray().add(9).add(1).add(2).add(3)));

        TestAPIUtil.put(BUSINESS_HOUR_API_ENDPOINT + "/" + ID, item,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertUpdateEntityTestResult(BusinessHourConfigStore.getStore(), CONTEXT, response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_UPDATED, ENTITY_NAME), LOGGER, testInfo.getTestMethod().get().getName());

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(6)
    void testGetReferences(VertxTestContext testContext)
    {
        TestAPIUtil.get(BUSINESS_HOUR_API_ENDPOINT + "/" + ID + "/references",
                testContext.succeeding(response -> testContext.verify(() ->
                {
                    assertEquals(SC_OK, response.statusCode());

                    var body = response.bodyAsJsonObject();

                    Assertions.assertNotNull(body);

                    assertEquals(SC_OK, body.getInteger(RESPONSE_CODE));

                    Assertions.assertNotNull(body.getJsonObject(RESULT));

                    testContext.completeNow();

                })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(7)
    void testDeleteBusinessHour(VertxTestContext testContext)
    {
        TestAPIUtil.delete(BUSINESS_HOUR_API_ENDPOINT + "/" + ID,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertDeleteEntityTestResult(BusinessHourConfigStore.getStore(), response.bodyAsJsonObject(),
                                    String.format(InfoMessageConstants.ENTITY_DELETED, ENTITY_NAME));

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(8)
    void testDeleteBusinessHourNotExist(VertxTestContext testContext)
    {
        TestAPIUtil.delete(BUSINESS_HOUR_API_ENDPOINT + ID, testContext.succeeding(response -> testContext.verify(() ->
        {
            TestAPIUtil.assertNotExistEntityDeleteTestResult(response, ENTITY_NAME, String.format(ErrorMessageConstants.ITEM_NOT_FOUND_IN_STORE, APIConstants.Entity.BUSINESS_HOUR.getName()));

            testContext.completeNow();

        })));
    }
}

