/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.api;

import com.mindarray.*;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.plugin.PluginEngineConstants;
import com.mindarray.runbook.Runbook;
import com.mindarray.store.RemoteEventProcessorConfigStore;
import com.mindarray.store.RunbookPluginConfigStore;
import com.mindarray.store.StorageProfileConfigStore;
import com.mindarray.util.CodecUtil;
import com.mindarray.util.Logger;
import io.github.artsok.RepeatedIfExceptionsTest;
import io.vertx.core.eventbus.MessageConsumer;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.junit5.Timeout;
import io.vertx.junit5.VertxExtension;
import io.vertx.junit5.VertxTestContext;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.eventbus.EventBusConstants.*;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

@ExtendWith(VertxExtension.class)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY$")
@Timeout(50 * 1000)
@Execution(ExecutionMode.SAME_THREAD)
public class TestStorageProfile
{

    private static final Logger LOGGER = new Logger(TestStorageProfile.class, "Storage TEST CASE", "Test Storage Profile");

    private static final String TEST_STORAGE_PROFILE_NAME = "Test-Storage-Profile";
    private static final JsonObject CONTEXT = new JsonObject().put(StorageProfile.STORAGE_PROFILE_NAME, TEST_STORAGE_PROFILE_NAME)
            .put(StorageProfile.STORAGE_PROFILE_PROTOCOL, StorageProfile.StorageProtocol.TFTP.getName());
    private static final String HOST = "************";
    private static Long STORAGE_PROFILE_ID = null;
    private static MessageConsumer<JsonObject> messageConsumer;

    @BeforeAll
    static void beforeAll(VertxTestContext testContext)
    {
        LOGGER.info("RemoteEventProcessorConfigStore: " + RemoteEventProcessorConfigStore.getStore().getItems().encode());

        RunbookPluginConfigStore.getStore().addItem(10000000000022L, new JsonObject().put(RunbookPlugin.RUNBOOK_PLUGIN_NAME, StorageProfile.StorageProtocol.FTP.getName())
                .put(RunbookPlugin.RUNBOOK_PLUGIN_TYPE, "Custom Script")
                .put(RunbookPlugin.RUNBOOK_PLUGIN_ENTITY_TYPE, EVENT_SOURCE)
                .put(RunbookPlugin.RUNBOOK_PLUGIN_ENTITIES, new JsonArray())
                .put(RunbookPlugin.RUNBOOK_PLUGIN_VARIABLES, new JsonObject())
                .put(RunbookPlugin.RUNBOOK_PLUGIN_CATEGORY, Runbook.RunbookCategory.STORAGE_PROFILE.getName())
                .put(RunbookPlugin.RUNBOOK_PLUGIN_CONTEXT, new JsonObject().put(PluginEngineConstants.SCRIPT, "package main\n\nimport (\n\t\"encoding/base64\"\n\t\"encoding/json\"\n\t\"fmt\"\n\t\"motadatasdk/consts\"\n\t. \"motadatasdk/motadatatypes\"\n\t\"motadatasdk/utils\"\n\t\"os\"\n)\n\nfunc main() {\n\n\tcontext, err := utils.LoadPluginContext(os.Args[2:][0])\n\n\tif err != nil {\n\n\t\tbytes, _ := json.Marshal(MotadataMap{\n\n\t\t\tconsts.Status: consts.StatusFail,\n\n\t\t\tconsts.Errors: []MotadataStringMap{\n\t\t\t\t{\n\t\t\t\t\tconsts.ErrorCode: consts.ErrorCodeInternalError,\n\t\t\t\t\tconsts.Error:     fmt.Sprintf(\"%v\", err),\n\t\t\t\t\tconsts.Message:   \"Failed to load context\",\n\t\t\t\t}},\n\t\t})\n\n\t\tfmt.Println(base64.StdEncoding.EncodeToString(bytes) + consts.BlankString)\n\n\t} else {\n\t\t\n\t\tcontext[consts.Status] = consts.StatusSucceed\n\t\t\n\t\tbytes, err := json.Marshal(context)\n\n\t\tif err != nil {\n\n\t\t\tcontext[consts.Status] = consts.StatusFail\n\n\t\t\terrors := []MotadataStringMap{\n\t\t\t\t{\n\t\t\t\t\tconsts.ErrorCode: consts.ErrorCodeInternalError,\n\t\t\t\t\tconsts.Error:     fmt.Sprintf(\"%v\", err),\n\t\t\t\t\tconsts.Message:   \"Error occurred while marshalling Result\",\n\t\t\t\t}}\n\n\t\t\tcontext[consts.Errors] = append(context.GetStringMapSliceValue(consts.Errors), errors...)\n\t\t}\n\n\t\tfmt.Println(base64.StdEncoding.EncodeToString(bytes) + consts.BlankString)\n\t}\n}")
                        .put(PluginEngineConstants.SCRIPT_LANGUAGE, "go")
                        .put(GlobalConstants.TIME, 60))
                .put(GlobalConstants.ID, 10000000000022L)
                .put("_type", 0));

        RunbookPluginConfigStore.getStore().addItem(10000000000023L, new JsonObject().put(RunbookPlugin.RUNBOOK_PLUGIN_NAME, StorageProfile.StorageProtocol.TFTP.getName())
                .put(RunbookPlugin.RUNBOOK_PLUGIN_TYPE, "Custom Script")
                .put(RunbookPlugin.RUNBOOK_PLUGIN_ENTITY_TYPE, EVENT_SOURCE)
                .put(RunbookPlugin.RUNBOOK_PLUGIN_ENTITIES, new JsonArray())
                .put(RunbookPlugin.RUNBOOK_PLUGIN_VARIABLES, new JsonObject())
                .put(RunbookPlugin.RUNBOOK_PLUGIN_CATEGORY, Runbook.RunbookCategory.STORAGE_PROFILE.getName())
                .put(RunbookPlugin.RUNBOOK_PLUGIN_CONTEXT, new JsonObject().put(PluginEngineConstants.SCRIPT, "package main\n\nimport (\n\t\"encoding/base64\"\n\t\"encoding/json\"\n\t\"fmt\"\n\t\"motadatasdk/consts\"\n\t. \"motadatasdk/motadatatypes\"\n\t\"motadatasdk/utils\"\n\t\"os\"\n)\n\nfunc main() {\n\n\tcontext, err := utils.LoadPluginContext(os.Args[2:][0])\n\n\tif err != nil {\n\n\t\tbytes, _ := json.Marshal(MotadataMap{\n\n\t\t\tconsts.Status: consts.StatusFail,\n\n\t\t\tconsts.Errors: []MotadataStringMap{\n\t\t\t\t{\n\t\t\t\t\tconsts.ErrorCode: consts.ErrorCodeInternalError,\n\t\t\t\t\tconsts.Error:     fmt.Sprintf(\"%v\", err),\n\t\t\t\t\tconsts.Message:   \"Failed to load context\",\n\t\t\t\t}},\n\t\t})\n\n\t\tfmt.Println(base64.StdEncoding.EncodeToString(bytes) + consts.BlankString)\n\n\t} else {\n\t\t\n\t\tcontext[consts.Status] = consts.StatusSucceed\n\t\t\n\t\tbytes, err := json.Marshal(context)\n\n\t\tif err != nil {\n\n\t\t\tcontext[consts.Status] = consts.StatusFail\n\n\t\t\terrors := []MotadataStringMap{\n\t\t\t\t{\n\t\t\t\t\tconsts.ErrorCode: consts.ErrorCodeInternalError,\n\t\t\t\t\tconsts.Error:     fmt.Sprintf(\"%v\", err),\n\t\t\t\t\tconsts.Message:   \"Error occurred while marshalling Result\",\n\t\t\t\t}}\n\n\t\t\tcontext[consts.Errors] = append(context.GetStringMapSliceValue(consts.Errors), errors...)\n\t\t}\n\n\t\tfmt.Println(base64.StdEncoding.EncodeToString(bytes) + consts.BlankString)\n\t}\n}")
                        .put(PluginEngineConstants.SCRIPT_LANGUAGE, "go")
                        .put(GlobalConstants.TIME, 60))
                .put(GlobalConstants.ID, 10000000000023L)
                .put("_type", 0));

        RunbookPluginConfigStore.getStore().addItem(10000000000024L, new JsonObject().put(RunbookPlugin.RUNBOOK_PLUGIN_NAME, StorageProfile.StorageProtocol.SFTP.getName())
                .put(RunbookPlugin.RUNBOOK_PLUGIN_TYPE, "Custom Script")
                .put(RunbookPlugin.RUNBOOK_PLUGIN_ENTITY_TYPE, EVENT_SOURCE)
                .put(RunbookPlugin.RUNBOOK_PLUGIN_ENTITIES, new JsonArray())
                .put(RunbookPlugin.RUNBOOK_PLUGIN_VARIABLES, new JsonObject())
                .put(RunbookPlugin.RUNBOOK_PLUGIN_CATEGORY, Runbook.RunbookCategory.STORAGE_PROFILE.getName())
                .put(RunbookPlugin.RUNBOOK_PLUGIN_CONTEXT, new JsonObject().put(PluginEngineConstants.SCRIPT, "package main\n\nimport (\n\t\"encoding/base64\"\n\t\"encoding/json\"\n\t\"fmt\"\n\t\"motadatasdk/consts\"\n\t. \"motadatasdk/motadatatypes\"\n\t\"motadatasdk/utils\"\n\t\"os\"\n)\n\nfunc main() {\n\n\tcontext, err := utils.LoadPluginContext(os.Args[2:][0])\n\n\tif err != nil {\n\n\t\tbytes, _ := json.Marshal(MotadataMap{\n\n\t\t\tconsts.Status: consts.StatusFail,\n\n\t\t\tconsts.Errors: []MotadataStringMap{\n\t\t\t\t{\n\t\t\t\t\tconsts.ErrorCode: consts.ErrorCodeInternalError,\n\t\t\t\t\tconsts.Error:     fmt.Sprintf(\"%v\", err),\n\t\t\t\t\tconsts.Message:   \"Failed to load context\",\n\t\t\t\t}},\n\t\t})\n\n\t\tfmt.Println(base64.StdEncoding.EncodeToString(bytes) + consts.BlankString)\n\n\t} else {\n\t\t\n\t\tcontext[consts.Status] = consts.StatusSucceed\n\t\t\n\t\tbytes, err := json.Marshal(context)\n\n\t\tif err != nil {\n\n\t\t\tcontext[consts.Status] = consts.StatusFail\n\n\t\t\terrors := []MotadataStringMap{\n\t\t\t\t{\n\t\t\t\t\tconsts.ErrorCode: consts.ErrorCodeInternalError,\n\t\t\t\t\tconsts.Error:     fmt.Sprintf(\"%v\", err),\n\t\t\t\t\tconsts.Message:   \"Error occurred while marshalling Result\",\n\t\t\t\t}}\n\n\t\t\tcontext[consts.Errors] = append(context.GetStringMapSliceValue(consts.Errors), errors...)\n\t\t}\n\n\t\tfmt.Println(base64.StdEncoding.EncodeToString(bytes) + consts.BlankString)\n\t}\n}")
                        .put(PluginEngineConstants.SCRIPT_LANGUAGE, "go")
                        .put(GlobalConstants.TIME, 60))
                .put(GlobalConstants.ID, 10000000000024L)
                .put("_type", 0));

        testContext.completeNow();
    }

    @BeforeEach
    void beforeEach(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running test case %s", testInfo.getTestMethod().get().getName()));

        testContext.completeNow();
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(1)
    void testCreateStorageProfileTFTP(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info("testStorageProfileTFTP: started");

        var context = CONTEXT.copy();

        context.put(StorageProfile.STORAGE_PROFILE_NAME, "Test TFTP Storage Profile")
                .put(StorageProfile.STORAGE_PROFILE_CONTEXT, new JsonObject().put(GlobalConstants.HOST, HOST).put(GlobalConstants.PORT, 69));

        LOGGER.info("testStorageProfileTFTP: context: " + context);

        TestAPIUtil.post(TestAPIConstants.STORAGE_PROFILE_API_ENDPOINT, context, testContext.succeeding(response ->
                testContext.verify(() ->
                {

                    var result = response.bodyAsJsonObject();

                    LOGGER.info("testStorageProfileTFTP: result: " + result);

                    TestAPIUtil.assertCreateEntityTestResult(StorageProfileConfigStore.getStore(), result, "Storage Profile created successfully", LOGGER, testInfo.getTestMethod().get().getName());

                    STORAGE_PROFILE_ID = result.getLong(GlobalConstants.ID);

                    testContext.completeNow();

                })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(2)
    void testCreateStorageProfileFTP(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info("testStorageProfileFTP: started");

        var context = CONTEXT.copy();

        context.put(StorageProfile.STORAGE_PROFILE_NAME, "Test FTP Storage Profile")
                .put(StorageProfile.STORAGE_PROFILE_PROTOCOL, StorageProfile.StorageProtocol.FTP.getName())
                .put(StorageProfile.STORAGE_PROFILE_CONTEXT, new JsonObject().put(GlobalConstants.HOST, HOST).put(GlobalConstants.PORT, 21).put(GlobalConstants.STORAGE_PATH, ""));

        LOGGER.info("testStorageProfileFTP: context: " + context);

        TestAPIUtil.post(TestAPIConstants.STORAGE_PROFILE_API_ENDPOINT, context, testContext.succeeding(response -> testContext.verify(() ->
        {

            LOGGER.info("testStorageProfileFTP: result:" + response.bodyAsJsonObject());

            TestAPIUtil.assertCreateEntityTestResult(StorageProfileConfigStore.getStore(), response.bodyAsJsonObject(), "Storage Profile created successfully", LOGGER, testInfo.getTestMethod().get().getName());

            testContext.completeNow();

        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(3)
    void testCreateStorageProfileSCP(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info("testStorageProfileSCP: started");

        var context = CONTEXT.copy();

        context.put(StorageProfile.STORAGE_PROFILE_NAME, "Test SCP Storage Profile")
                .put(StorageProfile.STORAGE_PROFILE_PROTOCOL, StorageProfile.StorageProtocol.SFTP.getName())
                .put(StorageProfile.STORAGE_PROFILE_CONTEXT, new JsonObject().put(GlobalConstants.HOST, HOST).put(GlobalConstants.PORT, 22));

        LOGGER.info("testStorageProfileSCP: context: " + context);

        TestAPIUtil.post(TestAPIConstants.STORAGE_PROFILE_API_ENDPOINT, context, testContext.succeeding(response ->
                testContext.verify(() ->
                {

                    LOGGER.info("testStorageProfileSCP: result: " + response.bodyAsJsonObject());

                    TestAPIUtil.assertCreateEntityTestResult(StorageProfileConfigStore.getStore(), response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_CREATED, APIConstants.Entity.STORAGE_PROFILE.getName()), LOGGER, testInfo.getTestMethod().get().getName());

                    testContext.completeNow();
                })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(4)
    void testCreateStorageProfileLocal(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info("testStorageProfileLocal: started");

        var context = CONTEXT.copy();

        context.put(StorageProfile.STORAGE_PROFILE_NAME, "Test LOCAL Storage Profile")
                .put(StorageProfile.STORAGE_PROFILE_PROTOCOL, StorageProfile.StorageProtocol.LOCAL.getName())
                .put(StorageProfile.STORAGE_PROFILE_CONTEXT, new JsonObject().put(GlobalConstants.STORAGE_PATH, CURRENT_DIR));

        LOGGER.info("testStorageProfileLocal: context: " + context);

        TestAPIUtil.post(TestAPIConstants.STORAGE_PROFILE_API_ENDPOINT, context, testContext.succeeding(response ->
                testContext.verify(() ->
                {

                    LOGGER.info("testStorageProfileLocal: result: " + response.bodyAsJsonObject());

                    TestAPIUtil.assertCreateEntityTestResult(StorageProfileConfigStore.getStore(), response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_CREATED, APIConstants.Entity.STORAGE_PROFILE.getName()), LOGGER, testInfo.getTestMethod().get().getName());

                    testContext.completeNow();
                })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(5)
    void testCreateStorageProfileDuplicateName(VertxTestContext testContext)
    {
        LOGGER.info("testStorageProfileWithDuplicateName: started");

        var context = CONTEXT.copy();

        context.put(StorageProfile.STORAGE_PROFILE_NAME, "Test TFTP Storage Profile")
                .put(StorageProfile.STORAGE_PROFILE_PROTOCOL, StorageProfile.StorageProtocol.TFTP.getName())
                .put(StorageProfile.STORAGE_PROFILE_CONTEXT, new JsonObject().put(GlobalConstants.HOST, HOST).put(GlobalConstants.PORT, 69));

        LOGGER.info("testStorageProfileWithDuplicateName: context: " + context);

        TestAPIUtil.post(TestAPIConstants.STORAGE_PROFILE_API_ENDPOINT, context, testContext.succeeding(response ->
                testContext.verify(() ->
                {

                    LOGGER.info("testStorageProfileWithDuplicateName: result: " + response.bodyAsJsonObject());

                    TestAPIUtil.assertAlreadyExistedEntityTestResult(response.bodyAsJsonObject(), "Storage Profile Name is not unique", StorageProfileConfigStore.getStore(), StorageProfile.STORAGE_PROFILE_NAME, context.getString(StorageProfile.STORAGE_PROFILE_NAME));

                    testContext.completeNow();
                })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(6)
    void testGetStorageProfile(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info("testStorageProfileWithDuplicateName: started");

        LOGGER.info("testStorageProfileWithDuplicateName: Storage_profile_id: " + STORAGE_PROFILE_ID);

        assertNotNull(STORAGE_PROFILE_ID);

        TestAPIUtil.get(TestAPIConstants.STORAGE_PROFILE_API_ENDPOINT + GlobalConstants.PATH_SEPARATOR + STORAGE_PROFILE_ID, testContext.succeeding(response ->
                testContext.verify(() ->
                {

                    LOGGER.info("testStorageProfileWithDuplicateName: result: " + response.bodyAsJsonObject());

                    TestAPIUtil.assertGETRequestTestResult(response, STORAGE_PROFILE_ID, StorageProfileConfigStore.getStore(), null, LOGGER, testInfo.getTestMethod().get().getName());

                    testContext.completeNow();
                })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(7)
    void testGetAllStorageProfiles(VertxTestContext testContext)
    {
        LOGGER.info("testStorageProfileGetAll: started");

        TestAPIUtil.get(TestAPIConstants.STORAGE_PROFILE_API_ENDPOINT, testContext.succeeding(response ->
                testContext.verify(() ->
                {

                    LOGGER.info("testStorageProfileGetAll: result: " + response.bodyAsJsonObject());

                    TestAPIUtil.assertGETAllRequestTestResult(response, StorageProfileConfigStore.getStore(), null);

                    testContext.completeNow();
                })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(9)
    void testUpdateStorageProfile(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info("testStorageProfileUpdate: started");

        LOGGER.info("testStorageProfileUpdate: Storage_profile_id: " + STORAGE_PROFILE_ID);

        assertNotNull(STORAGE_PROFILE_ID);

        var context = CONTEXT.copy();

        context.put(StorageProfile.STORAGE_PROFILE_PROTOCOL, StorageProfile.StorageProtocol.FTP.getName())
                .put(StorageProfile.STORAGE_PROFILE_CONTEXT, new JsonObject().put(GlobalConstants.HOST, HOST).put(GlobalConstants.PORT, 21).put(GlobalConstants.STORAGE_PATH, "/motadata/motadata"));

        LOGGER.info("testStorageProfileUpdate: context: " + context);

        TestAPIUtil.put(TestAPIConstants.STORAGE_PROFILE_API_ENDPOINT + GlobalConstants.PATH_SEPARATOR + STORAGE_PROFILE_ID, context, testContext.succeeding(response -> testContext.verify(() ->
        {

            TestAPIUtil.assertUpdateEntityTestResult(StorageProfileConfigStore.getStore(), context, response.bodyAsJsonObject(), "Storage Profile updated successfully", LOGGER, testInfo.getTestMethod().get().getName());

            testContext.completeNow();

        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(10)
    void testDeleteStorageProfileNotExist(VertxTestContext testContext)
    {
        TestAPIUtil.delete(TestAPIConstants.STORAGE_PROFILE_API_ENDPOINT + GlobalConstants.PATH_SEPARATOR + "123", testContext.succeeding(response ->
                testContext.verify(() ->
                {

                    TestAPIUtil.assertNotExistEntityDeleteTestResult(response, "Storage Profile", "Item Storage Profile not found");

                    testContext.completeNow();

                })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(11)
    void testStorageProfileFTP(VertxTestContext testContext)
    {
        try
        {
            LOGGER.info("testStorageProfileFTP: Building context");

            var context = new JsonObject().put(APIConstants.SESSION_ID, TestUtil.getSessionId())
                    .put(User.USER_NAME, "admin")
                    .put(StorageProfile.STORAGE_PROFILE_NAME, "FTP-Test")
                    .put(StorageProfile.STORAGE_PROFILE_PROTOCOL, StorageProfile.StorageProtocol.FTP.getName())
                    .put(StorageProfile.STORAGE_PROFILE_CONTEXT, new JsonObject().put(AIOpsObject.OBJECT_TARGET, HOST)
                            .put(GlobalConstants.PASSWORD, PASSWORD)
                            .put(GlobalConstants.USERNAME, USERNAME)
                            .put(GlobalConstants.PORT, 21));

            LOGGER.info("testStorageProfileFTP: context: " + context.encodePrettily());

            messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
            {

                if (message.body().containsKey(EVENT_TYPE) && message.body().getString(EVENT_TYPE).equalsIgnoreCase(UI_ACTION_STORAGE_PROFILE_TEST) && message.body().getBinary(EVENT_CONTEXT) != null)
                {
                    var result = CodecUtil.toJSONObject(message.body().getBinary(EventBusConstants.EVENT_CONTEXT));

                    LOGGER.info("testStorageProfileFTP: Result: " + result.encodePrettily());

                    assertEquals(InfoMessageConstants.STORAGE_PROFILE_TEST_SUCCEEDED, result.getString(MESSAGE));

                    assertEquals(STATUS_SUCCEED, result.getString(STATUS));

                    testContext.completeNow();

                    messageConsumer.unregister();
                }

            });

            TestUtil.vertx().eventBus().send(EventBusConstants.UI_ACTION_STORAGE_PROFILE_TEST, context);
        }
        catch (Exception exception)
        {
            LOGGER.info("testStorageProfileFTP: EXCEPTION: " + exception);
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(12)
    void testStorageProfileTFTP(VertxTestContext testContext)
    {

        try
        {
            LOGGER.info("testStorageProfileTFTP: Building context");

            var context = new JsonObject().put(APIConstants.SESSION_ID, TestUtil.getSessionId())
                    .put(User.USER_NAME, "admin")
                    .put(StorageProfile.STORAGE_PROFILE_NAME, "FTP-Test")
                    .put(StorageProfile.STORAGE_PROFILE_PROTOCOL, StorageProfile.StorageProtocol.TFTP.getName())
                    .put(StorageProfile.STORAGE_PROFILE_CONTEXT, new JsonObject().put(AIOpsObject.OBJECT_TARGET, HOST)
                            .put(GlobalConstants.PORT, 69));

            LOGGER.info("testStorageProfileTFTP: context: " + context.encodePrettily());

            messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
            {

                if (message.body().containsKey(EVENT_TYPE) && message.body().getString(EVENT_TYPE).equalsIgnoreCase(UI_ACTION_STORAGE_PROFILE_TEST) && message.body().getBinary(EVENT_CONTEXT) != null)
                {
                    var result = CodecUtil.toJSONObject(message.body().getBinary(EventBusConstants.EVENT_CONTEXT));

                    LOGGER.info("testStorageProfileTFTP: Result: " + result.encodePrettily());

                    assertEquals(InfoMessageConstants.STORAGE_PROFILE_TEST_SUCCEEDED, result.getString(MESSAGE));

                    assertEquals(STATUS_SUCCEED, result.getString(STATUS));

                    testContext.completeNow();

                    messageConsumer.unregister();
                }

            });

            TestUtil.vertx().eventBus().send(EventBusConstants.UI_ACTION_STORAGE_PROFILE_TEST, context);
        }
        catch (Exception exception)
        {
            LOGGER.info("testStorageProfileFTP: EXCEPTION: " + exception);
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(13)
    void testStorageProfileSFTP(VertxTestContext testContext)
    {
        var context = new JsonObject().put(APIConstants.SESSION_ID, TestUtil.getSessionId())
                .put(StorageProfile.STORAGE_PROFILE_NAME, "FTP-Test")
                .put(StorageProfile.STORAGE_PROFILE_PROTOCOL, StorageProfile.StorageProtocol.SFTP.getName())
                .put(StorageProfile.STORAGE_PROFILE_CONTEXT, new JsonObject().put(AIOpsObject.OBJECT_TARGET, HOST)
                        .put(GlobalConstants.PASSWORD, PASSWORD)
                        .put(GlobalConstants.USERNAME, USERNAME)
                        .put(GlobalConstants.PORT, 22));

        LOGGER.info("testStorageProfileSFTP: context: " + context.encodePrettily());

        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
        {

            if (message.body().containsKey(EVENT_TYPE) && message.body().getString(EVENT_TYPE).equalsIgnoreCase(UI_ACTION_STORAGE_PROFILE_TEST) && message.body().getBinary(EVENT_CONTEXT) != null)
            {
                var result = CodecUtil.toJSONObject(message.body().getBinary(EventBusConstants.EVENT_CONTEXT));

                LOGGER.info("testStorageProfileSFTP: Result: " + result.encodePrettily());

                assertEquals(InfoMessageConstants.STORAGE_PROFILE_TEST_SUCCEEDED, result.getString(MESSAGE));

                assertEquals(STATUS_SUCCEED, result.getString(STATUS));

                testContext.completeNow();

                messageConsumer.unregister();
            }

        });

        TestUtil.vertx().eventBus().send(EventBusConstants.UI_ACTION_STORAGE_PROFILE_TEST, context);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(14)
    void testStorageProfileLocal(VertxTestContext testContext)
    {
        var context = new JsonObject().put(APIConstants.SESSION_ID, TestUtil.getSessionId())
                .put(StorageProfile.STORAGE_PROFILE_NAME, "local-Test")
                .put(StorageProfile.STORAGE_PROFILE_PROTOCOL, StorageProfile.StorageProtocol.LOCAL.getName())
                .put(StorageProfile.STORAGE_PROFILE_CONTEXT, new JsonObject()
                        .put(GlobalConstants.DEST_FILE_NAME, "Storage-engine-test.txt")
                        .put(GlobalConstants.STORAGE_PATH, CURRENT_DIR + PATH_SEPARATOR + "config-management"));

        LOGGER.info("testStorageProfileLocal: context: " + context.encodePrettily());

        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
        {

            if (message.body().containsKey(EVENT_TYPE) && message.body().getString(EVENT_TYPE).equalsIgnoreCase(UI_ACTION_STORAGE_PROFILE_TEST) && message.body().getBinary(EVENT_CONTEXT) != null)
            {
                var result = CodecUtil.toJSONObject(message.body().getBinary(EventBusConstants.EVENT_CONTEXT));

                LOGGER.info("testStorageProfileLocal: Result: " + result.encodePrettily());

                assertEquals(InfoMessageConstants.STORAGE_PROFILE_TEST_SUCCEEDED, result.getString(MESSAGE));

                assertEquals(STATUS_SUCCEED, result.getString(STATUS));

                testContext.completeNow();

                messageConsumer.unregister();
            }

        });

        TestUtil.vertx().eventBus().send(EventBusConstants.UI_ACTION_STORAGE_PROFILE_TEST, context);
    }

}
