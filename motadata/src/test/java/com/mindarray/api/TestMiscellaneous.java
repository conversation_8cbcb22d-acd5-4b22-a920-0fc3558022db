/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.api;

import com.mindarray.GlobalConstants;
import com.mindarray.TestAPIConstants;
import com.mindarray.TestAPIUtil;
import com.mindarray.TestUtil;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.job.DailyMiscJobs;
import com.mindarray.job.JobScheduler;
import com.mindarray.notification.Notification;
import com.mindarray.store.DataRetentionPolicyConfigStore;
import com.mindarray.store.FlowSamplingRateConfigStore;
import com.mindarray.store.ObjectConfigStore;
import com.mindarray.util.LogUtil;
import com.mindarray.util.Logger;
import com.mindarray.util.PortUtil;
import com.mindarray.visualization.VisualizationConstants;
import io.github.artsok.RepeatedIfExceptionsTest;
import io.vertx.core.eventbus.MessageConsumer;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.junit5.VertxExtension;
import io.vertx.junit5.VertxTestContext;
import org.apache.commons.io.FileUtils;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;
import org.quartz.JobBuilder;
import org.quartz.TriggerBuilder;

import java.io.File;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.TestAPIConstants.*;
import static org.apache.http.HttpStatus.SC_OK;
import static org.junit.jupiter.api.Assertions.assertEquals;

@ExtendWith(VertxExtension.class)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@Execution(ExecutionMode.SAME_THREAD)
@EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY$")

public class TestMiscellaneous
{
    private static final Logger LOGGER = new Logger(TestMiscellaneous.class, "misc", "Misc Test");

    private static MessageConsumer<JsonObject> messageConsumer;

    private static String fileName;

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(1)
    void testGetGroupTypeMetricMapperAllMetrics(VertxTestContext testContext)
    {
        TestAPIUtil.get(MISC_COLUMN_MAPPER_API_ENDPOINT + "?filter=" + new JsonObject().put(VisualizationConstants.VISUALIZATION_GROUP_TYPE, VisualizationConstants.VisualizationDataSource.PERFORMANCE_METRIC.getName()),
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            var body = response.bodyAsJsonObject();

                            assertEquals(HttpStatus.SC_OK, body.getInteger(APIConstants.RESPONSE_CODE));

                            Assertions.assertNotNull(body.getJsonObject(GlobalConstants.RESULT));

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(2)
    void testGetGroupTypeMetricMapperMetricWithFilterMonitor(VertxTestContext testContext)
    {
        var object = ObjectConfigStore.getStore().getItemByIP("fd00:1:1:1::132");

        TestAPIUtil.get(MISC_COLUMN_MAPPER_API_ENDPOINT + "?filter=" + new JsonObject().put(ENTITIES, new JsonArray().add(object)).put(VisualizationConstants.VISUALIZATION_GROUP_TYPE, VisualizationConstants.VisualizationDataSource.PERFORMANCE_METRIC.getName()),
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            var body = response.bodyAsJsonObject();

                            assertEquals(HttpStatus.SC_OK, body.getInteger(APIConstants.RESPONSE_CODE));

                            Assertions.assertNotNull(body.getJsonObject(GlobalConstants.RESULT));

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(3)
    void testGetGroupTypeAvailabilityMapperAllMetrics(VertxTestContext testContext)
    {
        TestAPIUtil.get(MISC_COLUMN_MAPPER_API_ENDPOINT + "?filter=" + new JsonObject().put(VisualizationConstants.VISUALIZATION_GROUP_TYPE, VisualizationConstants.VisualizationDataSource.OBJECT_AVAILABILITY.getName()),
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            var body = response.bodyAsJsonObject();

                            assertEquals(HttpStatus.SC_OK, body.getInteger(APIConstants.RESPONSE_CODE));

                            Assertions.assertNotNull(body.getJsonObject(GlobalConstants.RESULT));

                            var metrics = body.getJsonObject(GlobalConstants.RESULT);

                            LOGGER.debug("Result for testGetGroupTypeAvailabilityMapperAllMetrics: " + metrics.encode());

                            for (var key : metrics.getMap().keySet())
                            {
                                Assertions.assertTrue(key.endsWith("percent") || key.endsWith("seconds") || key.endsWith("sql"));
                            }

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(4)
    void testGetGroupTypeAvailabilityMapperGaugeMetrics(VertxTestContext testContext)
    {
        TestAPIUtil.get(MISC_COLUMN_MAPPER_API_ENDPOINT + "?filter=" + new JsonObject().put(VisualizationConstants.VISUALIZATION_CATEGORY, VisualizationConstants.VisualizationCategory.GAUGE.getName()).put(VisualizationConstants.VISUALIZATION_GROUP_TYPE, VisualizationConstants.VisualizationDataSource.OBJECT_AVAILABILITY.getName()),
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            var body = response.bodyAsJsonObject();

                            assertEquals(HttpStatus.SC_OK, body.getInteger(APIConstants.RESPONSE_CODE));

                            Assertions.assertNotNull(body.getJsonObject(GlobalConstants.RESULT));

                            var metrics = body.getJsonObject(GlobalConstants.RESULT);

                            for (var key : metrics.getMap().keySet())
                            {
                                Assertions.assertTrue(key.endsWith(".count"));
                            }
                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(5)
    void testPortUtilUp(VertxTestContext testContext)
    {
        PortUtil.isOpen(new JsonArray().add("172.16.10.98"), 22).onComplete(response ->
        {
            if (response.succeeded())
            {
                Assertions.assertNotNull(response.result());

                var result = response.result().getJsonObject(0);

                Assertions.assertEquals(GlobalConstants.STATUS_UP, result.getString(GlobalConstants.STATUS));

                testContext.completeNow();
            }
        });
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(6)
    void testPortUtilDown(VertxTestContext testContext)
    {
        PortUtil.isOpen(new JsonArray().add("172.16.10.98"), 7777).onComplete(response ->
        {
            if (response.succeeded())
            {
                Assertions.assertNotNull(response.result());

                var result = response.result().getJsonObject(0);

                Assertions.assertEquals(GlobalConstants.STATUS_DOWN, result.getString(GlobalConstants.STATUS));

                testContext.completeNow();
            }
        });
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(7)
    void testPortUtilAsyncSocket0(VertxTestContext testContext)
    {
        PortUtil.isOpen("172.16.10.98", 7777).onComplete(response ->
        {
            if (response.succeeded())
            {
                Assertions.assertNotNull(response.result());

                var result = response.result();

                Assertions.assertEquals(GlobalConstants.STATUS_DOWN, result.getString(GlobalConstants.STATUS));

                testContext.completeNow();
            }
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(8)
    void testPortUtilSyncSocket(VertxTestContext testContext)
    {
        PortUtil.isOpen("172.16.10.98", 22, PortUtil.Method.SYNC_SOCKET).onComplete(response ->
        {
            if (response.succeeded())
            {
                Assertions.assertNotNull(response.result());

                var result = response.result();

                Assertions.assertEquals(GlobalConstants.STATUS_UP, result.getString(GlobalConstants.STATUS));

                testContext.completeNow();
            }
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(9)
    void testPortUtilAsyncSocket1(VertxTestContext testContext)
    {
        PortUtil.isOpen("172.16.10.98", 22, 5).onComplete(response ->
        {
            if (response.succeeded())
            {
                Assertions.assertNotNull(response.result());

                var result = response.result();

                Assertions.assertEquals(GlobalConstants.STATUS_UP, result.getString(GlobalConstants.STATUS));

                testContext.completeNow();
            }
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(10)
    void testLogFileRetention(VertxTestContext testContext) throws Exception
    {
        LOGGER.debug("Dumping Agent Test Debug Log");

        LOGGER.warn("Dumping Agent Test Warning Log");

        LOGGER.fatal("Dumping Agent Test Fatal Log");

        LOGGER.info("Dumping Agent Test info Logs");

        LogUtil.qualifyLogFiles(-2, true);

        testContext.awaitCompletion(1, TimeUnit.SECONDS);

        var agentLogFile = new File(CURRENT_DIR + PATH_SEPARATOR + LOGS_DIR);

        Assertions.assertTrue(agentLogFile.exists());

        var listLogFile = agentLogFile.listFiles();

        Assertions.assertNotNull(listLogFile);

        var fileSize = Objects.requireNonNull(listLogFile).length;

        Assertions.assertNotEquals(0, fileSize);

        var jobKey = "log-retention-job-test";

        var field = JobScheduler.class.getDeclaredField("scheduler");

        field.setAccessible(true);

        org.quartz.Scheduler scheduler = (org.quartz.Scheduler) field.get(JobScheduler.class);

        scheduler.scheduleJob(JobBuilder.newJob(DailyMiscJobs.class).withIdentity(jobKey, "system-job").build(),
                TriggerBuilder.newTrigger().withIdentity(jobKey + ".trigger", "temp-job").build());

        testContext.awaitCompletion(2, TimeUnit.SECONDS);

        testContext.completeNow();
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(11)
    void testLogFileGenerateArtifacts(VertxTestContext testContext) throws Exception
    {
        var path = GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + "agent-logs.zip";

        var file = new File(path);

        if (file.exists())
        {
            file.delete();
        }

        LogUtil.zipLogFiles(new JsonObject());

        TimeUnit.SECONDS.sleep(5);

        Assertions.assertTrue(new File(path).exists());

        testContext.completeNow();
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(14)
    void testGetFlowInterfaces(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = new JsonObject().put(ENTITIES, new JsonArray().add("**********"));

        TestAPIUtil.get(TestAPIConstants.MISC_FLOW_INTERFACE_API_ENDPOINT + "?filter=" + context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            LOGGER.info("Running ... " + testInfo.getTestMethod().get().getName());

                            LOGGER.info("FlowSamplingRateConfigStore.getStore().getItemsByValue(EventBusConstants.EVENT_SOURCE, \"**********\") : " + FlowSamplingRateConfigStore.getStore().getItemsByValue(EventBusConstants.EVENT_SOURCE, "**********"));

                            LOGGER.info(TestAPIConstants.MISC_FLOW_INTERFACE_API_ENDPOINT + " Response : " + response.bodyAsJsonObject().getJsonObject(RESULT));

                            Assertions.assertEquals(SC_OK, response.statusCode());

                            Assertions.assertEquals(STATUS_SUCCEED, response.bodyAsJsonObject().getString(GlobalConstants.STATUS));

                            Assertions.assertFalse(response.bodyAsJsonObject().getJsonObject(RESULT).isEmpty());

                            Assertions.assertEquals(FlowSamplingRateConfigStore.getStore().getItemsByValue(EventBusConstants.EVENT_SOURCE, "**********").size(), response.bodyAsJsonObject().getJsonObject(RESULT).size());

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(15)
    void testShareWidget(VertxTestContext testContext)
    {
        var context = new JsonObject().put("file", "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAApgAAAKYB3X3/OAAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAAANCSURBVEiJtZZPbBtFFMZ/M7ubXdtdb1xSFyeilBapySVU8h8OoFaooFSqiihIVIpQBKci6KEg9Q6H9kovIHoCIVQJJCKE1ENFjnAgcaSGC6rEnxBwA04Tx43t2FnvDAfjkNibxgHxnWb2e/u992bee7tCa00YFsffekFY+nUzFtjW0LrvjRXrCDIAaPLlW0nHL0SsZtVoaF98mLrx3pdhOqLtYPHChahZcYYO7KvPFxvRl5XPp1sN3adWiD1ZAqD6XYK1b/dvE5IWryTt2udLFedwc1+9kLp+vbbpoDh+6TklxBeAi9TL0taeWpdmZzQDry0AcO+jQ12RyohqqoYoo8RDwJrU+qXkjWtfi8Xxt58BdQuwQs9qC/afLwCw8tnQbqYAPsgxE1S6F3EAIXux2oQFKm0ihMsOF71dHYx+f3NND68ghCu1YIoePPQN1pGRABkJ6Bus96CutRZMydTl+TvuiRW1m3n0eDl0vRPcEysqdXn+jsQPsrHMquGeXEaY4Yk4wxWcY5V/9scqOMOVUFthatyTy8QyqwZ+kDURKoMWxNKr2EeqVKcTNOajqKoBgOE28U4tdQl5p5bwCw7BWquaZSzAPlwjlithJtp3pTImSqQRrb2Z8PHGigD4RZuNX6JYj6wj7O4TFLbCO/Mn/m8R+h6rYSUb3ekokRY6f/YukArN979jcW+V/S8g0eT/N3VN3kTqWbQ428m9/8k0P/1aIhF36PccEl6EhOcAUCrXKZXXWS3XKd2vc/TRBG9O5ELC17MmWubD2nKhUKZa26Ba2+D3P+4/MNCFwg59oWVeYhkzgN/JDR8deKBoD7Y+ljEjGZ0sosXVTvbc6RHirr2reNy1OXd6pJsQ+gqjk8VWFYmHrwBzW/n+uMPFiRwHB2I7ih8ciHFxIkd/3Omk5tCDV1t+2nNu5sxxpDFNx+huNhVT3/zMDz8usXC3ddaHBj1GHj/As08fwTS7Kt1HBTmyN29vdwAw+/wbwLVOJ3uAD1wi/dUH7Qei66PfyuRj4Ik9is+hglfbkbfR3cnZm7chlUWLdwmprtCohX4HUtlOcQjLYCu+fzGJH2QRKvP3UNz8bWk1qMxjGTOMThZ3kvgLI5AzFfo379UAAAAASUVORK5CYII=");

        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EventBusConstants.EVENT_NOTIFICATION, message ->
        {

            Assertions.assertEquals(Notification.NotificationType.EMAIL.getName(), message.body().getString(Notification.NOTIFICATION_TYPE));

            Assertions.assertEquals("***********", message.body().getJsonArray(Notification.EMAIL_NOTIFICATION_RECIPIENTS).getString(0));

            messageConsumer.unregister();

            var file = new File(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + UPLOADS + PATH_SEPARATOR + fileName);

            if (file.exists())
            {
                FileUtils.deleteQuietly(file);
            }

            testContext.completeNow();
        });

        TestAPIUtil.post(UPLOAD_IMAGE_ENDPOINT, context, testContext.succeeding(response ->

                testContext.verify(() ->
                {
                    Assertions.assertEquals(SC_OK, response.statusCode());

                    Assertions.assertEquals(STATUS_SUCCEED, response.bodyAsJsonObject().getString(GlobalConstants.STATUS));

                    fileName = response.bodyAsJsonObject().getString(FILE_NAME);

                    Assertions.assertNotNull(fileName);

                    Assertions.assertTrue(TestUtil.vertx().fileSystem().existsBlocking(CURRENT_DIR + PATH_SEPARATOR + UPLOADS + PATH_SEPARATOR + fileName));

                    TestUtil.vertx().eventBus().send(EventBusConstants.UI_ACTION_SHARE, new JsonObject().put("type", Notification.ShareType.WIDGET.getName()).put("recipients", new JsonArray().add(new JsonObject("{\"type\": \"email\",\"recipient\": \"***********\"}"))).put(MESSAGE, "").put(User.USER_NAME, "admin").put("filename", fileName));

                })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(16)
    void testUpdateDataRetentionPolicy(VertxTestContext testContext)
    {
        var context = DataRetentionPolicyConfigStore.getStore().getItems().getJsonObject(0);

        context.getJsonObject(DataRetentionPolicy.DATA_RETENTION_POLICY_CONTEXT).getJsonObject("PERFORMANCE_METRIC").put("raw", 185);

        TestAPIUtil.put(DATA_RETENTION_API_ENDPOINT + context.getLong(ID), context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            Assertions.assertEquals(SC_OK, response.statusCode());

                            Assertions.assertEquals(185, DataRetentionPolicyConfigStore.getStore().getItems().getJsonObject(0).getJsonObject(DataRetentionPolicy.DATA_RETENTION_POLICY_CONTEXT).getJsonObject("PERFORMANCE_METRIC").getInteger("raw"));

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(17)
    void testGetGeoDBCountries(VertxTestContext testContext)
    {
        TestAPIUtil.get(MISC_GEO_DB_COUNTRIES_API_ENDPOINT,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            Assertions.assertEquals(SC_OK, response.statusCode());

                            Assertions.assertEquals(259, response.bodyAsJsonObject().getJsonArray("result").size());

                            testContext.completeNow();
                        })));
    }
}
