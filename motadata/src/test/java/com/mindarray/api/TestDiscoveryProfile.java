/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.api;

import com.mindarray.*;
import com.mindarray.config.ConfigConstants;
import com.mindarray.nms.NMSConstants;
import com.mindarray.store.CredentialProfileConfigStore;
import com.mindarray.store.RemoteEventProcessorConfigStore;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.junit5.Timeout;
import io.vertx.junit5.VertxExtension;
import io.vertx.junit5.VertxTestContext;
import org.apache.commons.io.FileUtils;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

import static com.mindarray.GlobalConstants.*;
import static org.apache.http.HttpStatus.SC_OK;
import static org.junit.jupiter.api.Assertions.assertEquals;

@ExtendWith(VertxExtension.class)
@Timeout(30 * 1000)
@Execution(ExecutionMode.CONCURRENT)
@EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^AGENT|^PRIMARY$")
public class TestDiscoveryProfile
{
    public static final Map<String, JsonArray> CREDENTIAL_PROFILES = new ConcurrentHashMap<>();
    private static final JsonArray DISCOVERY_EVENT_PROCESSORS = new JsonArray()
            .add(RemoteEventProcessorConfigStore.getStore().getItemByValue(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID, Bootstrap.getRegistrationId())
                    .getLong(ID));
    private static final Logger LOGGER = new Logger(Discovery.class, MOTADATA_API, "Discovery API");

    @BeforeAll
    static void beforeAll(VertxTestContext testContext)
    {
        try
        {
            var file = new File(GlobalConstants.CURRENT_DIR + PATH_SEPARATOR + "src" + PATH_SEPARATOR
                    + "test" + PATH_SEPARATOR + "resources" + PATH_SEPARATOR + "discovery-parameters.json");

            if (file.exists())
            {
                new JsonObject(Files.readString(file.toPath(), StandardCharsets.UTF_8)).forEach(entry ->
                {
                    try
                    {
                        var discoveryContext = JsonObject.mapFrom(entry.getValue());

                        if (discoveryContext.containsKey(Discovery.DISCOVERY_CATEGORY))
                        {
                            if (discoveryContext.getString(Discovery.DISCOVERY_CATEGORY).equalsIgnoreCase(NMSConstants.Category.SERVER.getName()))
                            {
                                CREDENTIAL_PROFILES.computeIfAbsent(NMSConstants.Category.SERVER.getName(), value -> new JsonArray()).add(JsonObject.mapFrom(entry.getValue()));
                            }
                            else if (discoveryContext.getString(Discovery.DISCOVERY_CATEGORY).equalsIgnoreCase(NMSConstants.Category.NETWORK.getName()))
                            {
                                CREDENTIAL_PROFILES.computeIfAbsent(NMSConstants.Category.NETWORK.getName(), value -> new JsonArray()).add(JsonObject.mapFrom(entry.getValue()));
                            }
                            else if (discoveryContext.getString(Discovery.DISCOVERY_CATEGORY).equalsIgnoreCase(NMSConstants.Category.OTHER.getName()))
                            {
                                CREDENTIAL_PROFILES.computeIfAbsent(NMSConstants.Category.OTHER.getName(), value -> new JsonArray()).add(JsonObject.mapFrom(entry.getValue()));
                            }
                            else if (discoveryContext.getString(Discovery.DISCOVERY_CATEGORY).equalsIgnoreCase(NMSConstants.Category.VIRTUALIZATION.getName()))
                            {
                                CREDENTIAL_PROFILES.computeIfAbsent(NMSConstants.Category.VIRTUALIZATION.getName(), value -> new JsonArray()).add(JsonObject.mapFrom(entry.getValue()));
                            }
                            else if (discoveryContext.getString(Discovery.DISCOVERY_CATEGORY).equalsIgnoreCase(NMSConstants.Category.SERVICE_CHECK.getName()))
                            {
                                CREDENTIAL_PROFILES.computeIfAbsent(NMSConstants.Category.SERVICE_CHECK.getName(), value -> new JsonArray()).add(JsonObject.mapFrom(entry.getValue()));
                            }
                            else if (discoveryContext.getString(Discovery.DISCOVERY_CATEGORY).equalsIgnoreCase(NMSConstants.Category.CLOUD.getName()))
                            {
                                CREDENTIAL_PROFILES.computeIfAbsent(NMSConstants.Category.CLOUD.getName(), value -> new JsonArray()).add(JsonObject.mapFrom(entry.getValue()));
                            }
                            else if (discoveryContext.getString(Discovery.DISCOVERY_CATEGORY).equalsIgnoreCase(NMSConstants.Category.HCI.getName()))
                            {
                                CREDENTIAL_PROFILES.computeIfAbsent(NMSConstants.Category.HCI.getName(), value -> new JsonArray()).add(JsonObject.mapFrom(entry.getValue()));
                            }
                            else if (discoveryContext.getString(Discovery.DISCOVERY_CATEGORY).equalsIgnoreCase(NMSConstants.Category.SDN.getName()))
                            {
                                CREDENTIAL_PROFILES.computeIfAbsent(NMSConstants.Category.SDN.getName(), value -> new JsonArray()).add(JsonObject.mapFrom(entry.getValue()));
                            }
                            else if (discoveryContext.getString(Discovery.DISCOVERY_CATEGORY).equalsIgnoreCase(NMSConstants.Category.STORAGE.getName()))
                            {
                                CREDENTIAL_PROFILES.computeIfAbsent(NMSConstants.Category.STORAGE.getName(), value -> new JsonArray()).add(JsonObject.mapFrom(entry.getValue()));
                            }
                        }
                    }
                    catch (Exception exception)
                    {
                        LOGGER.error(exception);
                    }
                });
            }

            FileUtils.writeStringToFile(new File(ConfigConstants.CONFIG_MANAGEMENT_DEFAULT_DIR + PATH_SEPARATOR + "B_10000_R_16957341090.cfg"), "This is FTP 172.16.14.6 - Running");

            FileUtils.writeStringToFile(new File(ConfigConstants.CONFIG_MANAGEMENT_DEFAULT_DIR + PATH_SEPARATOR + "B_10000_S_16957341111.cfg"), "This is FTP 172.16.14.6 - Startup");

            testContext.completeNow();
        }
        catch (Exception exception)
        {
            testContext.failNow(exception.getMessage());

            exception.printStackTrace();
        }
    }

    @Test
    @EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY$")
    void testCreateServerCategoryDiscoveryProfiles(VertxTestContext testContext)
    {
        createDiscoveryProfiles(CREDENTIAL_PROFILES.get(NMSConstants.Category.SERVER.getName()), new AtomicInteger(0), testContext);
    }

    @Test
    @EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY$")
    void testCreateNetworkCategoryDiscoveryProfiles(VertxTestContext testContext)
    {
        createDiscoveryProfiles(CREDENTIAL_PROFILES.get(NMSConstants.Category.NETWORK.getName()), new AtomicInteger(0), testContext);
    }

    @Test
    @EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY$")
    void testOtherCategoryDiscoveryProfiles(VertxTestContext testContext)
    {
        createDiscoveryProfiles(CREDENTIAL_PROFILES.get(NMSConstants.Category.OTHER.getName()), new AtomicInteger(0), testContext);
    }

    @Test
    @EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY$")
    void testVirtualizationCategoryDiscoveryProfiles(VertxTestContext testContext)
    {
        createDiscoveryProfiles(CREDENTIAL_PROFILES.get(NMSConstants.Category.VIRTUALIZATION.getName()), new AtomicInteger(0), testContext);
    }

    @Test
    @EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY$")
    void testCloudCategoryDiscoveryProfiles(VertxTestContext testContext)
    {
        createDiscoveryProfiles(CREDENTIAL_PROFILES.get(NMSConstants.Category.CLOUD.getName()), new AtomicInteger(0), testContext);
    }

    @Test
    @EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^AGENT|^PRIMARY$")
    void testServiceCheckCategoryDiscoveryProfiles(VertxTestContext testContext)
    {
        createDiscoveryProfiles(CREDENTIAL_PROFILES.get(NMSConstants.Category.SERVICE_CHECK.getName()), new AtomicInteger(0), testContext);
    }

    @Test
    @EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY$")
    void testHCICategoryDiscoveryProfiles(VertxTestContext testContext)
    {
        createDiscoveryProfiles(CREDENTIAL_PROFILES.get(NMSConstants.Category.HCI.getName()), new AtomicInteger(0), testContext);
    }

    @Test
    @EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY$")
    void testSDNCategoryDiscoveryProfiles(VertxTestContext testContext)
    {
        createDiscoveryProfiles(CREDENTIAL_PROFILES.get(NMSConstants.Category.SDN.getName()), new AtomicInteger(0), testContext);
    }

    @Test
    @EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY$")
    void testStorageCategoryDiscoveryProfiles(VertxTestContext testContext)
    {
        createDiscoveryProfiles(CREDENTIAL_PROFILES.get(NMSConstants.Category.STORAGE.getName()), new AtomicInteger(0), testContext);
    }


    void createDiscoveryProfiles(JsonArray profiles, AtomicInteger index, VertxTestContext testContext)
    {
        try
        {
            if (index.get() >= profiles.size())
            {
                testContext.completeNow();
            }
            else
            {
                TestUtil.vertx().setTimer(100, id ->
                {
                    try
                    {
                        var discoveryContext = profiles.getJsonObject(index.get());

                        if (discoveryContext.containsKey("discovery.credential.profile.context"))
                        {
                            var credentialProfileIds = new JsonArray();

                            for (var object : discoveryContext.getJsonArray("discovery.credential.profile.context"))
                            {
                                credentialProfileIds.add(CredentialProfileConfigStore.getStore().getItemByValue(CredentialProfile.CREDENTIAL_PROFILE_NAME, ((JsonObject) object).getString(CredentialProfile.CREDENTIAL_PROFILE_NAME)).getLong(ID));
                            }

                            discoveryContext.put(Discovery.DISCOVERY_CREDENTIAL_PROFILES, credentialProfileIds);
                        }
                        else
                        {
                            discoveryContext.put(Discovery.DISCOVERY_CREDENTIAL_PROFILES, new JsonArray());
                        }

                        discoveryContext.put(Discovery.DISCOVERY_GROUPS, new JsonArray().add(CommonUtil.getLong("10000000000013")));

                        discoveryContext.put(Discovery.DISCOVERY_EVENT_PROCESSORS, DISCOVERY_EVENT_PROCESSORS);

                        createDiscoveryProfile(profiles.getJsonObject(index.get()), testContext).onComplete(result ->
                        {
                            try
                            {
                                if (result.succeeded())
                                {
                                    index.set(index.incrementAndGet());

                                    createDiscoveryProfiles(profiles, index, testContext);
                                }
                                else
                                {
                                    testContext.failNow(result.cause());

                                    LOGGER.error(result.cause());
                                }
                            }
                            catch (Exception exception)
                            {
                                LOGGER.error(exception);

                                testContext.failNow(exception);
                            }
                        });
                    }
                    catch (Exception exception)
                    {
                        LOGGER.error(exception);

                        testContext.failNow(exception);
                    }
                });
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            testContext.failNow(exception);
        }
    }

    Future<Long> createDiscoveryProfile(JsonObject context, VertxTestContext vertxTestContext)
    {
        var promise = Promise.<Long>promise();

        if (context != null && !context.isEmpty())
        {
            TestAPIUtil.post(TestAPIConstants.DISCOVERY_API_ENDPOINT, context, vertxTestContext.succeeding(result ->
                    vertxTestContext.verify(() ->
                    {
                        assertEquals(SC_OK, result.statusCode());

                        promise.complete(result.bodyAsJsonObject().getLong(ID));
                    })));
        }
        else
        {
            promise.complete();
        }

        return promise.future();
    }
}